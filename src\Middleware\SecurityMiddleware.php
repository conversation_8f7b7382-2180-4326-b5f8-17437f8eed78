<?php

namespace TelegramBot\Middleware;

use TelegramBot\Services\ValidationService;
use TelegramBot\Services\FileService;

/**
 * Security Middleware
 * 
 * Handles security checks, input validation, and threat detection.
 */
class SecurityMiddleware
{
    private $validationService;
    private $fileService;
    private $securityConfig;

    public function __construct(ValidationService $validationService, FileService $fileService)
    {
        $this->validationService = $validationService;
        $this->fileService = $fileService;
        $this->loadSecurityConfig();
    }

    /**
     * Load security configuration
     */
    private function loadSecurityConfig()
    {
        $this->securityConfig = [
            'max_message_length' => MAX_MESSAGE_LENGTH,
            'rate_limit_messages' => RATE_LIMIT_MESSAGES,
            'rate_limit_time' => RATE_LIMIT_TIME,
            'blocked_words' => $this->loadBlockedWords(),
            'allowed_file_types' => ['photo', 'video', 'document', 'audio', 'voice', 'sticker'],
            'max_file_size' => 50 * 1024 * 1024, // 50MB
            'suspicious_patterns' => [
                '/(.)\1{20,}/', // Repeated characters
                '/[A-Z]{30,}/', // Too many capitals
                '/(.{1,5})\1{10,}/', // Repeated short patterns
            ],
            'blocked_domains' => [
                'bit.ly',
                'tinyurl.com',
                'short.link',
                'suspicious-domain.com'
            ]
        ];
    }

    /**
     * Load blocked words from file
     */
    private function loadBlockedWords()
    {
        $blockedWordsFile = ADMIN_PATH . 'blocked_words.txt';
        
        if (file_exists($blockedWordsFile)) {
            $words = file($blockedWordsFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            return array_map('trim', $words);
        }

        return [];
    }

    /**
     * Main security middleware
     */
    public function validateSecurity($update, $next)
    {
        // Extract relevant data
        $userId = $this->extractUserId($update);
        $message = $update['message'] ?? null;
        $callbackQuery = $update['callback_query'] ?? null;

        // Validate user ID
        if ($userId && !$this->validationService->validateUserId($userId)) {
            return $this->blockRequest('Invalid user ID', $update);
        }

        // Check rate limiting
        if ($userId && !$this->checkRateLimit($userId)) {
            return $this->blockRequest('Rate limit exceeded', $update);
        }

        // Validate message if present
        if ($message && !$this->validateMessage($message)) {
            return $this->blockRequest('Invalid message content', $update);
        }

        // Validate callback query if present
        if ($callbackQuery && !$this->validateCallbackQuery($callbackQuery)) {
            return $this->blockRequest('Invalid callback query', $update);
        }

        // Check for suspicious activity
        if ($this->detectSuspiciousActivity($update)) {
            return $this->blockRequest('Suspicious activity detected', $update);
        }

        // All checks passed, continue
        return $next($update);
    }

    /**
     * Validate message content
     */
    private function validateMessage($message)
    {
        $text = $message['text'] ?? '';
        
        // Check message length
        if (strlen($text) > $this->securityConfig['max_message_length']) {
            $this->logSecurityEvent('Message too long', $message);
            return false;
        }

        // Check for blocked words
        if ($this->containsBlockedWords($text)) {
            $this->logSecurityEvent('Blocked words detected', $message);
            return false;
        }

        // Check for suspicious patterns
        if ($this->containsSuspiciousPatterns($text)) {
            $this->logSecurityEvent('Suspicious patterns detected', $message);
            return false;
        }

        // Check for malicious links
        if ($this->containsMaliciousLinks($text)) {
            $this->logSecurityEvent('Malicious links detected', $message);
            return false;
        }

        // Validate media if present
        if (!$this->validateMedia($message)) {
            return false;
        }

        return true;
    }

    /**
     * Validate callback query
     */
    private function validateCallbackQuery($callbackQuery)
    {
        $data = $callbackQuery['data'] ?? '';
        
        // Validate callback data format
        if (!$this->validationService->validateCallbackData($data)) {
            $this->logSecurityEvent('Invalid callback data', $callbackQuery);
            return false;
        }

        // Check for injection attempts
        if ($this->validationService->hasSqlInjection($data) || 
            $this->validationService->hasXss($data)) {
            $this->logSecurityEvent('Injection attempt in callback', $callbackQuery);
            return false;
        }

        return true;
    }

    /**
     * Check for blocked words
     */
    private function containsBlockedWords($text)
    {
        $text = strtolower($text);
        
        foreach ($this->securityConfig['blocked_words'] as $word) {
            if (strpos($text, strtolower($word)) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for suspicious patterns
     */
    private function containsSuspiciousPatterns($text)
    {
        foreach ($this->securityConfig['suspicious_patterns'] as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for malicious links
     */
    private function containsMaliciousLinks($text)
    {
        // Extract URLs from text
        $urls = $this->extractUrls($text);
        
        foreach ($urls as $url) {
            $domain = parse_url($url, PHP_URL_HOST);
            
            if (in_array($domain, $this->securityConfig['blocked_domains'])) {
                return true;
            }
            
            // Check for suspicious URL patterns
            if ($this->isSuspiciousUrl($url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract URLs from text
     */
    private function extractUrls($text)
    {
        $pattern = '/https?:\/\/[^\s]+/i';
        preg_match_all($pattern, $text, $matches);
        return $matches[0];
    }

    /**
     * Check if URL is suspicious
     */
    private function isSuspiciousUrl($url)
    {
        // Check for URL shorteners
        $shorteners = ['bit.ly', 'tinyurl.com', 'short.link', 't.co'];
        $domain = parse_url($url, PHP_URL_HOST);
        
        if (in_array($domain, $shorteners)) {
            return true;
        }

        // Check for suspicious patterns in URL
        $suspiciousPatterns = [
            '/[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/', // IP addresses
            '/[a-z0-9]{20,}\./', // Long random subdomains
            '/\.(tk|ml|ga|cf)$/', // Suspicious TLDs
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate media content
     */
    private function validateMedia($message)
    {
        $mediaTypes = ['photo', 'video', 'document', 'audio', 'voice', 'sticker'];
        
        foreach ($mediaTypes as $type) {
            if (isset($message[$type])) {
                if (!$this->validateMediaType($message[$type], $type)) {
                    $this->logSecurityEvent("Invalid $type media", $message);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Validate specific media type
     */
    private function validateMediaType($media, $type)
    {
        // Check file size if available
        if (isset($media['file_size']) && 
            $media['file_size'] > $this->securityConfig['max_file_size']) {
            return false;
        }

        // Check MIME type for documents
        if ($type === 'document' && isset($media['mime_type'])) {
            if ($this->isSuspiciousMimeType($media['mime_type'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check for suspicious MIME types
     */
    private function isSuspiciousMimeType($mimeType)
    {
        $suspiciousMimes = [
            'application/x-executable',
            'application/x-msdownload',
            'application/x-msdos-program',
            'application/x-winexe',
            'application/x-bat',
            'application/x-sh',
            'text/x-script.phps'
        ];

        return in_array($mimeType, $suspiciousMimes);
    }

    /**
     * Check rate limiting
     */
    private function checkRateLimit($userId)
    {
        return $this->validationService->checkMessageRate($userId);
    }

    /**
     * Detect suspicious activity
     */
    private function detectSuspiciousActivity($update)
    {
        $userId = $this->extractUserId($update);
        
        if (!$userId) {
            return false;
        }

        // Check for rapid-fire requests
        if ($this->isRapidFireActivity($userId)) {
            return true;
        }

        // Check for bot-like behavior
        if ($this->isBotLikeBehavior($update)) {
            return true;
        }

        return false;
    }

    /**
     * Check for rapid-fire activity
     */
    private function isRapidFireActivity($userId)
    {
        $activityFile = STORAGE_PATH . "security/activity_$userId.json";
        
        if (!file_exists($activityFile)) {
            return false;
        }

        $activity = json_decode(file_get_contents($activityFile), true);
        
        if (!$activity) {
            return false;
        }

        $recentActivity = array_filter($activity, function($timestamp) {
            return (time() - $timestamp) < 10; // Last 10 seconds
        });

        return count($recentActivity) > 20; // More than 20 requests in 10 seconds
    }

    /**
     * Check for bot-like behavior
     */
    private function isBotLikeBehavior($update)
    {
        // Check for identical repeated messages
        $message = $update['message'] ?? null;
        
        if ($message && isset($message['text'])) {
            $userId = $message['from']['id'];
            $text = $message['text'];
            
            return $this->isRepeatedMessage($userId, $text);
        }

        return false;
    }

    /**
     * Check for repeated messages
     */
    private function isRepeatedMessage($userId, $text)
    {
        $messagesFile = STORAGE_PATH . "security/messages_$userId.json";
        
        if (!file_exists($messagesFile)) {
            $this->saveUserMessage($userId, $text);
            return false;
        }

        $messages = json_decode(file_get_contents($messagesFile), true) ?: [];
        
        // Count identical messages in last hour
        $identicalCount = 0;
        $cutoffTime = time() - 3600;
        
        foreach ($messages as $msg) {
            if ($msg['timestamp'] > $cutoffTime && $msg['text'] === $text) {
                $identicalCount++;
            }
        }

        $this->saveUserMessage($userId, $text);
        
        return $identicalCount > 5; // More than 5 identical messages in an hour
    }

    /**
     * Save user message for analysis
     */
    private function saveUserMessage($userId, $text)
    {
        $securityDir = STORAGE_PATH . 'security/';
        if (!is_dir($securityDir)) {
            mkdir($securityDir, 0755, true);
        }

        $messagesFile = $securityDir . "messages_$userId.json";
        $messages = [];
        
        if (file_exists($messagesFile)) {
            $messages = json_decode(file_get_contents($messagesFile), true) ?: [];
        }

        $messages[] = [
            'text' => substr($text, 0, 100), // Store first 100 chars only
            'timestamp' => time()
        ];

        // Keep only last 50 messages
        if (count($messages) > 50) {
            $messages = array_slice($messages, -50);
        }

        file_put_contents($messagesFile, json_encode($messages));
    }

    /**
     * Log security event
     */
    private function logSecurityEvent($event, $data)
    {
        $this->fileService->logMessage('security', $event, [
            'user_id' => $this->extractUserId($data),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'data' => $this->sanitizeLogData($data)
        ]);
    }

    /**
     * Sanitize data for logging
     */
    private function sanitizeLogData($data)
    {
        // Remove sensitive information and limit size
        if (is_array($data)) {
            unset($data['message']['photo']);
            unset($data['message']['video']);
            unset($data['message']['document']);
            
            if (isset($data['message']['text'])) {
                $data['message']['text'] = substr($data['message']['text'], 0, 200);
            }
        }

        return $data;
    }

    /**
     * Block request and log
     */
    private function blockRequest($reason, $update)
    {
        $this->logSecurityEvent("Request blocked: $reason", $update);
        return false;
    }

    /**
     * Extract user ID from update
     */
    private function extractUserId($update)
    {
        if (is_array($update)) {
            if (isset($update['message']['from']['id'])) {
                return $update['message']['from']['id'];
            }
            
            if (isset($update['callback_query']['from']['id'])) {
                return $update['callback_query']['from']['id'];
            }
        }

        return null;
    }

    /**
     * Add blocked word
     */
    public function addBlockedWord($word)
    {
        $blockedWordsFile = ADMIN_PATH . 'blocked_words.txt';
        $words = $this->loadBlockedWords();
        
        if (!in_array($word, $words)) {
            $words[] = $word;
            file_put_contents($blockedWordsFile, implode("\n", $words));
            $this->securityConfig['blocked_words'] = $words;
            return true;
        }

        return false;
    }

    /**
     * Remove blocked word
     */
    public function removeBlockedWord($word)
    {
        $blockedWordsFile = ADMIN_PATH . 'blocked_words.txt';
        $words = $this->loadBlockedWords();
        
        $words = array_filter($words, function($w) use ($word) {
            return $w !== $word;
        });

        file_put_contents($blockedWordsFile, implode("\n", $words));
        $this->securityConfig['blocked_words'] = $words;
        return true;
    }

    /**
     * Get security statistics
     */
    public function getSecurityStatistics()
    {
        $securityLogFile = LOGS_PATH . date('Y-m-d') . '.log';
        $securityEvents = 0;
        
        if (file_exists($securityLogFile)) {
            $content = file_get_contents($securityLogFile);
            $securityEvents = substr_count($content, '[security]');
        }

        return [
            'blocked_words_count' => count($this->securityConfig['blocked_words']),
            'security_events_today' => $securityEvents,
            'blocked_domains_count' => count($this->securityConfig['blocked_domains']),
            'max_file_size' => $this->securityConfig['max_file_size'],
            'rate_limit_messages' => $this->securityConfig['rate_limit_messages']
        ];
    }
}
