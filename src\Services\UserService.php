<?php

namespace TelegramBot\Services;

use TelegramBot\TelegramAPI;

/**
 * User Service Class
 * 
 * Handles user management operations including admin management,
 * banning/unbanning users, and user statistics.
 */
class UserService
{
    private $fileService;
    private $api;

    public function __construct(FileService $fileService, TelegramAPI $api)
    {
        $this->fileService = $fileService;
        $this->api = $api;
    }

    /**
     * Register a new user
     */
    public function registerUser($userId, $userData = [])
    {
        // Add to members list
        $this->fileService->addMember($userId);
        
        // Update statistics
        $this->fileService->incrementStatistic('total_users');
        
        // Log new user registration
        $this->fileService->logMessage('info', "New user registered: $userId", $userData);
        
        return true;
    }

    /**
     * Check if user is a member
     */
    public function isMember($userId)
    {
        $members = $this->fileService->getMembers();
        return in_array($userId, $members);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin($userId)
    {
        return $this->fileService->isAdmin($userId) || $userId == MAIN_ADMIN_ID;
    }

    /**
     * Check if user is main admin
     */
    public function isMainAdmin($userId)
    {
        return $userId == MAIN_ADMIN_ID;
    }

    /**
     * Check if user is banned
     */
    public function isBanned($userId)
    {
        return $this->fileService->isBanned($userId);
    }

    /**
     * Add admin with validation
     */
    public function addAdmin($userId, $promotedBy)
    {
        // Check if promoter is main admin
        if (!$this->isMainAdmin($promotedBy)) {
            return ['success' => false, 'message' => ERROR_PERMISSION_DENIED];
        }

        // Check if user is already admin
        if ($this->isAdmin($userId)) {
            return ['success' => false, 'message' => 'المستخدم أدمن بالفعل'];
        }

        // Check admin limit
        $admins = $this->fileService->getAdmins();
        if (count($admins) >= MAX_ADMINS) {
            return ['success' => false, 'message' => 'تم الوصول للحد الأقصى من الأدمنية'];
        }

        // Add admin
        $this->fileService->addAdmin($userId);
        
        // Log admin addition
        $this->fileService->logMessage('info', "Admin added: $userId by $promotedBy");
        
        return ['success' => true, 'message' => SUCCESS_ADMIN_ADDED];
    }

    /**
     * Remove admin with validation
     */
    public function removeAdmin($userId, $removedBy)
    {
        // Check if remover is main admin
        if (!$this->isMainAdmin($removedBy)) {
            return ['success' => false, 'message' => ERROR_PERMISSION_DENIED];
        }

        // Check if user is main admin (cannot be removed)
        if ($this->isMainAdmin($userId)) {
            return ['success' => false, 'message' => 'لا يمكن تنزيل المطور الرئيسي'];
        }

        // Check if user is admin
        if (!$this->isAdmin($userId)) {
            return ['success' => false, 'message' => 'المستخدم ليس أدمن'];
        }

        // Remove admin
        $this->fileService->removeAdmin($userId);
        
        // Log admin removal
        $this->fileService->logMessage('info', "Admin removed: $userId by $removedBy");
        
        return ['success' => true, 'message' => SUCCESS_ADMIN_REMOVED];
    }

    /**
     * Ban user with validation
     */
    public function banUser($userId, $bannedBy, $reason = '')
    {
        // Check if banner is admin
        if (!$this->isAdmin($bannedBy)) {
            return ['success' => false, 'message' => ERROR_PERMISSION_DENIED];
        }

        // Check if user is main admin (cannot be banned)
        if ($this->isMainAdmin($userId)) {
            return ['success' => false, 'message' => 'لا يمكن حظر المطور الرئيسي'];
        }

        // Check if user is already banned
        if ($this->isBanned($userId)) {
            return ['success' => false, 'message' => 'المستخدم محظور بالفعل'];
        }

        // Ban user
        $this->fileService->banUser($userId);
        
        // Log ban
        $this->fileService->logMessage('warning', "User banned: $userId by $bannedBy", ['reason' => $reason]);
        
        // Notify user
        $this->api->sendMessage($userId, BANNED_MESSAGE);
        
        return ['success' => true, 'message' => SUCCESS_USER_BANNED];
    }

    /**
     * Unban user with validation
     */
    public function unbanUser($userId, $unbannedBy)
    {
        // Check if unbanner is admin
        if (!$this->isAdmin($unbannedBy)) {
            return ['success' => false, 'message' => ERROR_PERMISSION_DENIED];
        }

        // Check if user is banned
        if (!$this->isBanned($userId)) {
            return ['success' => false, 'message' => 'المستخدم غير محظور'];
        }

        // Unban user
        $this->fileService->unbanUser($userId);
        
        // Log unban
        $this->fileService->logMessage('info', "User unbanned: $userId by $unbannedBy");
        
        // Notify user
        $this->api->sendMessage($userId, 'تم إلغاء حظرك من البوت ✅');
        
        return ['success' => true, 'message' => SUCCESS_USER_UNBANNED];
    }

    /**
     * Get user information
     */
    public function getUserInfo($userId)
    {
        try {
            $chatInfo = $this->api->getChat($userId);
            
            if (!$chatInfo || !$chatInfo['ok']) {
                return null;
            }
            
            $user = $chatInfo['result'];
            
            return [
                'id' => $user['id'],
                'first_name' => $user['first_name'] ?? '',
                'last_name' => $user['last_name'] ?? '',
                'username' => $user['username'] ?? '',
                'is_admin' => $this->isAdmin($userId),
                'is_banned' => $this->isBanned($userId),
                'is_main_admin' => $this->isMainAdmin($userId)
            ];
        } catch (Exception $e) {
            $this->fileService->logMessage('error', "Error getting user info for $userId: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get admin list with details
     */
    public function getAdminList()
    {
        $admins = $this->fileService->getAdmins();
        $adminList = [];
        
        foreach ($admins as $adminId) {
            if (empty($adminId)) continue;
            
            $userInfo = $this->getUserInfo($adminId);
            if ($userInfo) {
                $adminList[] = $userInfo;
            }
        }
        
        return $adminList;
    }

    /**
     * Get user statistics
     */
    public function getUserStatistics()
    {
        $members = $this->fileService->getMembers();
        $admins = $this->fileService->getAdmins();
        $banned = $this->fileService->getBannedUsers();
        $stats = $this->fileService->getStatistics();
        
        return [
            'total_users' => count(array_filter($members)),
            'total_admins' => count(array_filter($admins)),
            'total_banned' => count(array_filter($banned)),
            'total_messages_received' => $stats['total_messages_received'] ?? 0,
            'total_messages_sent' => $stats['total_messages_sent'] ?? 0,
            'total_broadcasts' => $stats['total_broadcasts'] ?? 0,
            'bot_started' => $stats['bot_started'] ?? 'Unknown'
        ];
    }

    /**
     * Generate promotion code
     */
    public function generatePromotionCode()
    {
        $code = rand(111111, 999999);
        $this->fileService->writeFile(ADMIN_PATH . 'promotion_code.txt', $code);
        return $code;
    }

    /**
     * Verify promotion code
     */
    public function verifyPromotionCode($code)
    {
        $storedCode = $this->fileService->readFile(ADMIN_PATH . 'promotion_code.txt');
        return trim($code) === trim($storedCode);
    }

    /**
     * Clear promotion code
     */
    public function clearPromotionCode()
    {
        $this->fileService->deleteFile(ADMIN_PATH . 'promotion_code.txt');
    }

    /**
     * Promote user with code
     */
    public function promoteWithCode($userId, $code)
    {
        if (!$this->verifyPromotionCode($code)) {
            return ['success' => false, 'message' => 'رمز الترقية خاطئ ❌'];
        }

        if ($this->isAdmin($userId)) {
            return ['success' => false, 'message' => 'أنت أدمن بالفعل ✅'];
        }

        // Add admin
        $this->fileService->addAdmin($userId);
        
        // Clear the code
        $this->clearPromotionCode();
        
        // Log promotion
        $this->fileService->logMessage('info', "User promoted with code: $userId");
        
        return ['success' => true, 'message' => 'تم ترقيتك بنجاح ✅'];
    }

    /**
     * Check user permissions for action
     */
    public function hasPermission($userId, $action)
    {
        switch ($action) {
            case 'admin_panel':
                return $this->isAdmin($userId);
            case 'manage_admins':
                return $this->isMainAdmin($userId);
            case 'ban_users':
                return $this->isAdmin($userId);
            case 'broadcast':
                return $this->isAdmin($userId);
            case 'manage_channels':
                return $this->isMainAdmin($userId);
            case 'view_statistics':
                return $this->isAdmin($userId);
            default:
                return false;
        }
    }

    /**
     * Rate limiting check
     */
    public function checkRateLimit($userId)
    {
        $rateLimitFile = STORAGE_PATH . "rate_limit/$userId.txt";
        
        if (!file_exists($rateLimitFile)) {
            $this->fileService->createDirectory(dirname($rateLimitFile));
            $this->fileService->writeFile($rateLimitFile, time() . ':1');
            return true;
        }
        
        $data = explode(':', $this->fileService->readFile($rateLimitFile));
        $lastTime = (int)$data[0];
        $count = (int)$data[1];
        
        $currentTime = time();
        
        // Reset counter if time window passed
        if ($currentTime - $lastTime > RATE_LIMIT_TIME) {
            $this->fileService->writeFile($rateLimitFile, $currentTime . ':1');
            return true;
        }
        
        // Check if limit exceeded
        if ($count >= RATE_LIMIT_MESSAGES) {
            return false;
        }
        
        // Increment counter
        $this->fileService->writeFile($rateLimitFile, $lastTime . ':' . ($count + 1));
        return true;
    }
}
