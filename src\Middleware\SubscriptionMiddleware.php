<?php

namespace TelegramBot\Middleware;

use TelegramBot\TelegramAPI;
use TelegramBot\Services\FileService;
use TelegramBot\Services\UserService;

/**
 * Subscription Middleware
 * 
 * Handles forced subscription checks and channel membership verification.
 */
class SubscriptionMiddleware
{
    private $api;
    private $fileService;
    private $userService;

    public function __construct(TelegramAPI $api, FileService $fileService, UserService $userService)
    {
        $this->api = $api;
        $this->fileService = $fileService;
        $this->userService = $userService;
    }

    /**
     * Check if subscription verification is enabled
     */
    public function isSubscriptionCheckEnabled()
    {
        $settings = $this->fileService->getSettings();
        return $settings['subscription_check'] ?? ENABLE_SUBSCRIPTION_CHECK;
    }

    /**
     * Check if user is subscribed to all required channels
     */
    public function isUserSubscribed($userId)
    {
        // Skip check if disabled
        if (!$this->isSubscriptionCheckEnabled()) {
            return true;
        }

        // Skip check for admins
        if ($this->userService->isAdmin($userId)) {
            return true;
        }

        $channels = $this->getRequiredChannels();
        
        if (empty($channels)) {
            return true; // No channels required
        }

        foreach ($channels as $channel) {
            if (!$this->isSubscribedToChannel($userId, $channel)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if user is subscribed to specific channel
     */
    public function isSubscribedToChannel($userId, $channel)
    {
        try {
            $result = $this->api->getChatMember($channel, $userId);
            
            if (!$result || !$result['ok']) {
                $this->fileService->logMessage('warning', 
                    "Failed to check subscription for user $userId in channel $channel",
                    ['error' => $result['description'] ?? 'Unknown error']
                );
                return false;
            }

            $status = $result['result']['status'];
            $allowedStatuses = ['member', 'administrator', 'creator'];
            
            return in_array($status, $allowedStatuses);

        } catch (Exception $e) {
            $this->fileService->logMessage('error', 
                "Exception checking subscription: " . $e->getMessage(),
                ['user_id' => $userId, 'channel' => $channel]
            );
            return false;
        }
    }

    /**
     * Get list of required channels
     */
    public function getRequiredChannels()
    {
        $channels = $this->fileService->getChannels();
        
        // Add default channel if configured
        if (defined('CHANNEL_USERNAME') && CHANNEL_USERNAME) {
            $channels[] = CHANNEL_USERNAME;
        }

        return array_unique(array_filter($channels));
    }

    /**
     * Middleware to enforce subscription
     */
    public function enforceSubscription($update, $next)
    {
        $userId = $this->extractUserId($update);
        
        if (!$userId) {
            return false;
        }

        if (!$this->isUserSubscribed($userId)) {
            return $this->handleUnsubscribedUser($update);
        }

        // User is subscribed, continue
        return $next($update);
    }

    /**
     * Handle unsubscribed user
     */
    private function handleUnsubscribedUser($update)
    {
        $userId = $this->extractUserId($update);
        $chatId = $this->extractChatId($update);
        
        if (!$chatId) {
            return false;
        }

        // Send subscription required message
        $this->sendSubscriptionMessage($chatId, $userId);
        
        // Log subscription violation
        $this->fileService->logMessage('info', 
            "Subscription required for user $userId",
            ['user_id' => $userId, 'chat_id' => $chatId]
        );

        return false; // Stop processing
    }

    /**
     * Send subscription required message
     */
    public function sendSubscriptionMessage($chatId, $userId = null)
    {
        $channels = $this->getRequiredChannels();
        $buttons = [];

        foreach ($channels as $channel) {
            $channelInfo = $this->getChannelInfo($channel);
            $channelName = $channelInfo['title'] ?? str_replace('@', '', $channel);
            $channelUrl = $this->getChannelUrl($channel);
            
            $buttons[] = [['text' => "📢 $channelName", 'url' => $channelUrl]];
        }

        // Add check subscription button
        $buttons[] = [['text' => '✅ تحقق من الاشتراك', 'callback_data' => 'check_subscription']];

        $keyboard = TelegramAPI::createInlineKeyboard($buttons);

        $message = $this->getSubscriptionMessage();

        return $this->api->sendMessage($chatId, $message, [
            'parse_mode' => 'Markdown',
            'reply_markup' => $keyboard,
            'disable_web_page_preview' => true
        ]);
    }

    /**
     * Get subscription required message
     */
    private function getSubscriptionMessage()
    {
        $customMessage = $this->fileService->readFile(ADMIN_PATH . 'subscription_message.txt');
        
        if ($customMessage) {
            return trim($customMessage);
        }

        return SUBSCRIPTION_REQUIRED_MESSAGE;
    }

    /**
     * Get channel information
     */
    public function getChannelInfo($channel)
    {
        try {
            $result = $this->api->getChat($channel);
            
            if ($result && $result['ok']) {
                return $result['result'];
            }
        } catch (Exception $e) {
            $this->fileService->logMessage('warning', 
                "Failed to get channel info for $channel: " . $e->getMessage()
            );
        }

        return ['title' => str_replace('@', '', $channel)];
    }

    /**
     * Get channel URL
     */
    private function getChannelUrl($channel)
    {
        $username = str_replace('@', '', $channel);
        return "https://t.me/$username";
    }

    /**
     * Handle subscription check callback
     */
    public function handleSubscriptionCheck($callbackQuery)
    {
        $userId = $callbackQuery['from']['id'];
        $chatId = $callbackQuery['message']['chat']['id'];
        $messageId = $callbackQuery['message']['message_id'];

        // Answer callback query first
        $this->api->answerCallbackQuery($callbackQuery['id'], 'جاري التحقق...');

        if ($this->isUserSubscribed($userId)) {
            // User is now subscribed
            $this->api->editMessageText($chatId, $messageId, 
                "✅ تم التحقق من اشتراكك بنجاح!\nيمكنك الآن استخدام البوت."
            );

            // Send welcome message
            $this->sendWelcomeAfterSubscription($chatId, $userId);
            
            return true;
        } else {
            // Still not subscribed
            $this->api->answerCallbackQuery($callbackQuery['id'], 
                'يجب عليك الاشتراك في جميع القنوات أولاً', true
            );
            
            return false;
        }
    }

    /**
     * Send welcome message after successful subscription
     */
    private function sendWelcomeAfterSubscription($chatId, $userId)
    {
        $welcomeMessage = $this->fileService->getWelcomeMessage();
        
        $keyboard = TelegramAPI::createInlineKeyboard([
            [
                ['text' => 'قناة البوت', 'url' => 't.me/VEVoGamez'],
                ['text' => 'المطور', 'url' => 't.me/GoogleYooz']
            ]
        ]);

        $this->api->sendMessage($chatId, $welcomeMessage, [
            'parse_mode' => 'Markdown',
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Extract user ID from update
     */
    private function extractUserId($update)
    {
        if (isset($update['message']['from']['id'])) {
            return $update['message']['from']['id'];
        }
        
        if (isset($update['callback_query']['from']['id'])) {
            return $update['callback_query']['from']['id'];
        }

        return null;
    }

    /**
     * Extract chat ID from update
     */
    private function extractChatId($update)
    {
        if (isset($update['message']['chat']['id'])) {
            return $update['message']['chat']['id'];
        }
        
        if (isset($update['callback_query']['message']['chat']['id'])) {
            return $update['callback_query']['message']['chat']['id'];
        }

        return null;
    }

    /**
     * Add required channel
     */
    public function addRequiredChannel($channel)
    {
        // Validate channel format
        if (!preg_match('/^@?[a-zA-Z][a-zA-Z0-9_]{4,31}$/', $channel)) {
            return false;
        }

        // Ensure channel starts with @
        if (!str_starts_with($channel, '@')) {
            $channel = '@' . $channel;
        }

        // Check if bot is admin in the channel
        if (!$this->isBotAdminInChannel($channel)) {
            return false;
        }

        return $this->fileService->addChannel($channel);
    }

    /**
     * Remove required channel
     */
    public function removeRequiredChannel($channel)
    {
        return $this->fileService->removeChannel($channel);
    }

    /**
     * Check if bot is admin in channel
     */
    public function isBotAdminInChannel($channel)
    {
        try {
            $botInfo = $this->api->makeRequest('getMe');
            
            if (!$botInfo || !$botInfo['ok']) {
                return false;
            }

            $botId = $botInfo['result']['id'];
            $result = $this->api->getChatMember($channel, $botId);
            
            if (!$result || !$result['ok']) {
                return false;
            }

            $status = $result['result']['status'];
            return in_array($status, ['administrator', 'creator']);

        } catch (Exception $e) {
            $this->fileService->logMessage('error', 
                "Error checking bot admin status in $channel: " . $e->getMessage()
            );
            return false;
        }
    }

    /**
     * Get subscription statistics
     */
    public function getSubscriptionStatistics()
    {
        $channels = $this->getRequiredChannels();
        $stats = [];

        foreach ($channels as $channel) {
            $channelInfo = $this->getChannelInfo($channel);
            $stats[] = [
                'channel' => $channel,
                'title' => $channelInfo['title'] ?? 'Unknown',
                'member_count' => $channelInfo['members_count'] ?? 0,
                'is_bot_admin' => $this->isBotAdminInChannel($channel)
            ];
        }

        return $stats;
    }

    /**
     * Bulk check user subscriptions
     */
    public function bulkCheckSubscriptions($userIds)
    {
        $results = [];
        
        foreach ($userIds as $userId) {
            $results[$userId] = $this->isUserSubscribed($userId);
        }

        return $results;
    }
}
