<?php

namespace TelegramBot\Services;

/**
 * File Service Class
 * 
 * Handles all file operations for data persistence and storage management.
 */
class FileService
{
    private $config;

    public function __construct($config = null)
    {
        $this->config = $config;

        // If no config provided or config is missing files array, create default config
        if (!$this->config || !isset($this->config['files'])) {
            $this->initializeDefaultConfig();
        }
    }

    /**
     * Initialize default configuration
     */
    private function initializeDefaultConfig()
    {
        $this->config = [
            'files' => [
                'members' => USERS_PATH . 'members.txt',
                'admins' => ADMIN_PATH . 'admins.txt',
                'banned' => USERS_PATH . 'banned.txt',
                'channels' => ADMIN_PATH . 'channels.txt',
                'settings' => ADMIN_PATH . 'settings.json',
                'statistics' => ADMIN_PATH . 'statistics.json',
                'welcome_message' => ADMIN_PATH . 'welcome.txt',
                'auto_reply' => ADMIN_PATH . 'auto_reply.txt'
            ],
            'directories' => [
                'storage' => STORAGE_PATH,
                'users' => USERS_PATH,
                'messages' => MESSAGES_PATH,
                'admin' => ADMIN_PATH,
                'logs' => LOGS_PATH,
                'temp' => STORAGE_PATH . 'temp/',
                'backups' => STORAGE_PATH . 'backups/'
            ]
        ];
    }

    /**
     * Get file path from config with fallback
     */
    private function getFilePath($key)
    {
        if (isset($this->config['files'][$key])) {
            return $this->config['files'][$key];
        }

        // Fallback to default paths if config is missing
        $fallbackPaths = [
            'members' => USERS_PATH . 'members.txt',
            'admins' => ADMIN_PATH . 'admins.txt',
            'banned' => USERS_PATH . 'banned.txt',
            'channels' => ADMIN_PATH . 'channels.txt',
            'settings' => ADMIN_PATH . 'settings.json',
            'statistics' => ADMIN_PATH . 'statistics.json',
            'welcome_message' => ADMIN_PATH . 'welcome.txt',
            'auto_reply' => ADMIN_PATH . 'auto_reply.txt'
        ];

        return $fallbackPaths[$key] ?? '';
    }

    /**
     * Read data from file
     */
    public function readFile($filename)
    {
        if (!file_exists($filename)) {
            return '';
        }
        return file_get_contents($filename);
    }

    /**
     * Write data to file
     */
    public function writeFile($filename, $data, $append = false)
    {
        $flag = $append ? FILE_APPEND | LOCK_EX : LOCK_EX;
        return file_put_contents($filename, $data, $flag);
    }

    /**
     * Read lines from file as array
     */
    public function readLines($filename)
    {
        if (!file_exists($filename)) {
            return [];
        }
        $content = trim(file_get_contents($filename));
        return $content ? explode("\n", $content) : [];
    }

    /**
     * Write lines to file
     */
    public function writeLines($filename, $lines)
    {
        $content = is_array($lines) ? implode("\n", $lines) : $lines;
        return $this->writeFile($filename, $content);
    }

    /**
     * Append line to file
     */
    public function appendLine($filename, $line)
    {
        return $this->writeFile($filename, $line . "\n", true);
    }

    /**
     * Remove line from file
     */
    public function removeLine($filename, $line)
    {
        $lines = $this->readLines($filename);
        $lines = array_filter($lines, function($l) use ($line) {
            return trim($l) !== trim($line);
        });
        return $this->writeLines($filename, $lines);
    }

    /**
     * Check if line exists in file
     */
    public function lineExists($filename, $line)
    {
        $lines = $this->readLines($filename);
        return in_array(trim($line), array_map('trim', $lines));
    }

    /**
     * Read JSON data from file
     */
    public function readJson($filename)
    {
        if (!file_exists($filename)) {
            return [];
        }
        $content = file_get_contents($filename);
        return json_decode($content, true) ?: [];
    }

    /**
     * Write JSON data to file
     */
    public function writeJson($filename, $data)
    {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return $this->writeFile($filename, $json);
    }

    /**
     * Update JSON data in file
     */
    public function updateJson($filename, $key, $value)
    {
        $data = $this->readJson($filename);
        $data[$key] = $value;
        return $this->writeJson($filename, $data);
    }

    /**
     * Get file size
     */
    public function getFileSize($filename)
    {
        return file_exists($filename) ? filesize($filename) : 0;
    }

    /**
     * Delete file
     */
    public function deleteFile($filename)
    {
        return file_exists($filename) ? unlink($filename) : true;
    }

    /**
     * Create directory if it doesn't exist
     */
    public function createDirectory($path, $permissions = 0755)
    {
        if (!is_dir($path)) {
            return mkdir($path, $permissions, true);
        }
        return true;
    }

    /**
     * Get all members
     */
    public function getMembers()
    {
        return $this->readLines($this->getFilePath('members'));
    }

    /**
     * Add member
     */
    public function addMember($userId)
    {
        if (!$this->lineExists($this->getFilePath('members'), $userId)) {
            return $this->appendLine($this->getFilePath('members'), $userId);
        }
        return true;
    }

    /**
     * Get all admins
     */
    public function getAdmins()
    {
        return $this->readLines($this->getFilePath('admins'));
    }

    /**
     * Add admin
     */
    public function addAdmin($userId)
    {
        if (!$this->lineExists($this->getFilePath('admins'), $userId)) {
            return $this->appendLine($this->getFilePath('admins'), $userId);
        }
        return true;
    }

    /**
     * Remove admin
     */
    public function removeAdmin($userId)
    {
        return $this->removeLine($this->getFilePath('admins'), $userId);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin($userId)
    {
        return $this->lineExists($this->getFilePath('admins'), $userId);
    }

    /**
     * Get banned users
     */
    public function getBannedUsers()
    {
        return $this->readLines($this->getFilePath('banned'));
    }

    /**
     * Ban user
     */
    public function banUser($userId)
    {
        if (!$this->lineExists($this->getFilePath('banned'), $userId)) {
            return $this->appendLine($this->getFilePath('banned'), $userId);
        }
        return true;
    }

    /**
     * Unban user
     */
    public function unbanUser($userId)
    {
        return $this->removeLine($this->getFilePath('banned'), $userId);
    }

    /**
     * Check if user is banned
     */
    public function isBanned($userId)
    {
        return $this->lineExists($this->getFilePath('banned'), $userId);
    }

    /**
     * Get channels
     */
    public function getChannels()
    {
        $channels = $this->readLines($this->getFilePath('channels'));
        return array_filter($channels);
    }

    /**
     * Add channel
     */
    public function addChannel($channel)
    {
        if (!$this->lineExists($this->getFilePath('channels'), $channel)) {
            return $this->appendLine($this->getFilePath('channels'), $channel);
        }
        return true;
    }

    /**
     * Remove channel
     */
    public function removeChannel($channel)
    {
        return $this->removeLine($this->getFilePath('channels'), $channel);
    }

    /**
     * Get settings
     */
    public function getSettings()
    {
        return $this->readJson($this->getFilePath('settings'));
    }

    /**
     * Update setting
     */
    public function updateSetting($key, $value)
    {
        return $this->updateJson($this->getFilePath('settings'), $key, $value);
    }

    /**
     * Get statistics
     */
    public function getStatistics()
    {
        return $this->readJson($this->getFilePath('statistics'));
    }

    /**
     * Update statistics
     */
    public function updateStatistics($key, $value)
    {
        return $this->updateJson($this->getFilePath('statistics'), $key, $value);
    }

    /**
     * Increment statistics counter
     */
    public function incrementStatistic($key)
    {
        $stats = $this->getStatistics();
        $stats[$key] = ($stats[$key] ?? 0) + 1;
        return $this->writeJson($this->getFilePath('statistics'), $stats);
    }

    /**
     * Get welcome message
     */
    public function getWelcomeMessage()
    {
        return $this->readFile($this->getFilePath('welcome_message')) ?: WELCOME_MESSAGE;
    }

    /**
     * Set welcome message
     */
    public function setWelcomeMessage($message)
    {
        return $this->writeFile($this->getFilePath('welcome_message'), $message);
    }

    /**
     * Get auto reply message
     */
    public function getAutoReplyMessage()
    {
        return $this->readFile($this->getFilePath('auto_reply')) ?: AUTO_REPLY_MESSAGE;
    }

    /**
     * Set auto reply message
     */
    public function setAutoReplyMessage($message)
    {
        return $this->writeFile($this->getFilePath('auto_reply'), $message);
    }

    /**
     * Log message
     */
    public function logMessage($level, $message, $context = [])
    {
        $logFile = LOGS_PATH . date('Y-m-d') . '.log';
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = $context ? ' ' . json_encode($context) : '';
        $logEntry = "[$timestamp] [$level] $message$contextStr\n";
        return $this->writeFile($logFile, $logEntry, true);
    }

    /**
     * Clean old log files (keep last 30 days)
     */
    public function cleanOldLogs($days = 30)
    {
        $cutoffDate = date('Y-m-d', strtotime("-$days days"));
        $logFiles = glob(LOGS_PATH . '*.log');
        
        foreach ($logFiles as $file) {
            $fileDate = basename($file, '.log');
            if ($fileDate < $cutoffDate) {
                unlink($file);
            }
        }
    }
}
