<?php
/**
 * Bot Setup and Management Interface
 * 
 * This file provides a web interface for setting up and managing the bot.
 * Access this file through your browser to configure the bot.
 */

// Include required files
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/TelegramAPI.php';
require_once __DIR__ . '/../src/Services/FileService.php';
require_once __DIR__ . '/../src/Services/UserService.php';
require_once __DIR__ . '/../src/Services/MessageService.php';
require_once __DIR__ . '/../src/Services/ValidationService.php';
require_once __DIR__ . '/../src/Controllers/AdminController.php';
require_once __DIR__ . '/../src/Controllers/UserController.php';
require_once __DIR__ . '/../src/Controllers/BroadcastController.php';
require_once __DIR__ . '/../src/Controllers/GameController.php';
require_once __DIR__ . '/../src/Router.php';
require_once __DIR__ . '/../src/Bot.php';

// Initialize bot
$bot = new TelegramBot\Bot();
$api = $bot->getApi();

// Get current URL for webhook
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$webhookUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']) . '/webhook.php';

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'set_webhook':
                $result = $bot->setWebhook($webhookUrl);
                if ($result) {
                    $message = 'Webhook set successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to set webhook. Error: ' . $api->getLastError();
                    $messageType = 'error';
                }
                break;

            case 'delete_webhook':
                $result = $bot->deleteWebhook();
                if ($result) {
                    $message = 'Webhook deleted successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to delete webhook. Error: ' . $api->getLastError();
                    $messageType = 'error';
                }
                break;

            case 'test_bot':
                $result = $bot->sendTestMessage();
                if ($result) {
                    $message = 'Test message sent successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to send test message. Error: ' . $api->getLastError();
                    $messageType = 'error';
                }
                break;

            case 'maintenance':
                $result = $bot->performMaintenance();
                if ($result) {
                    $message = 'Maintenance tasks completed successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Some maintenance tasks failed. Check logs for details.';
                    $messageType = 'warning';
                }
                break;
        }
    }
}

// Get bot info
$botInfo = $bot->getBotInfo();
$webhookInfo = $bot->getWebhookInfo();
$statistics = $bot->getStatistics();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo BOT_NAME; ?> - Setup & Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 500;
        }

        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #6c757d;
        }

        .info-value {
            color: #495057;
            text-align: right;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        }

        .btn.success {
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        }

        .actions {
            text-align: center;
            margin-top: 30px;
        }

        .status {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .status.active {
            background: #d4edda;
            color: #155724;
        }

        .status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .webhook-url {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo BOT_NAME; ?></h1>
            <p>Setup & Management Interface</p>
        </div>

        <div class="content">
            <?php if ($message): ?>
                <div class="alert <?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <div class="grid">
                <!-- Bot Information -->
                <div class="card">
                    <h3>🤖 Bot Information</h3>
                    <?php if ($botInfo): ?>
                        <div class="info-item">
                            <span class="info-label">Bot Name:</span>
                            <span class="info-value"><?php echo htmlspecialchars($botInfo['first_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Username:</span>
                            <span class="info-value">@<?php echo htmlspecialchars($botInfo['username']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Bot ID:</span>
                            <span class="info-value"><?php echo $botInfo['id']; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Can Join Groups:</span>
                            <span class="info-value"><?php echo $botInfo['can_join_groups'] ? 'Yes' : 'No'; ?></span>
                        </div>
                    <?php else: ?>
                        <p>❌ Unable to fetch bot information</p>
                    <?php endif; ?>
                </div>

                <!-- Webhook Information -->
                <div class="card">
                    <h3>🔗 Webhook Status</h3>
                    <?php if ($webhookInfo): ?>
                        <div class="info-item">
                            <span class="info-label">Status:</span>
                            <span class="info-value">
                                <?php if (!empty($webhookInfo['url'])): ?>
                                    <span class="status active">Active</span>
                                <?php else: ?>
                                    <span class="status inactive">Not Set</span>
                                <?php endif; ?>
                            </span>
                        </div>
                        <?php if (!empty($webhookInfo['url'])): ?>
                            <div class="info-item">
                                <span class="info-label">URL:</span>
                                <span class="info-value"><?php echo htmlspecialchars($webhookInfo['url']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Pending Updates:</span>
                                <span class="info-value"><?php echo $webhookInfo['pending_update_count']; ?></span>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <p>❌ Unable to fetch webhook information</p>
                    <?php endif; ?>
                    
                    <div class="webhook-url">
                        <strong>Webhook URL:</strong><br>
                        <?php echo htmlspecialchars($webhookUrl); ?>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="card">
                    <h3>📊 Statistics</h3>
                    <?php if ($statistics): ?>
                        <div class="info-item">
                            <span class="info-label">Total Users:</span>
                            <span class="info-value"><?php echo number_format($statistics['total_users'] ?? 0); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Messages Received:</span>
                            <span class="info-value"><?php echo number_format($statistics['total_messages_received'] ?? 0); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Messages Sent:</span>
                            <span class="info-value"><?php echo number_format($statistics['total_messages_sent'] ?? 0); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Broadcasts:</span>
                            <span class="info-value"><?php echo number_format($statistics['total_broadcasts'] ?? 0); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Bot Started:</span>
                            <span class="info-value"><?php echo $statistics['bot_started'] ?? 'Unknown'; ?></span>
                        </div>
                    <?php else: ?>
                        <p>❌ Unable to fetch statistics</p>
                    <?php endif; ?>
                </div>

                <!-- System Information -->
                <div class="card">
                    <h3>⚙️ System Information</h3>
                    <div class="info-item">
                        <span class="info-label">Bot Version:</span>
                        <span class="info-value"><?php echo BOT_VERSION; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">PHP Version:</span>
                        <span class="info-value"><?php echo PHP_VERSION; ?></span>
                    </div>
                    <?php if ($statistics): ?>
                        <div class="info-item">
                            <span class="info-label">Memory Usage:</span>
                            <span class="info-value"><?php echo round(($statistics['memory_usage'] ?? 0) / 1024 / 1024, 2); ?> MB</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Peak Memory:</span>
                            <span class="info-value"><?php echo round(($statistics['memory_peak'] ?? 0) / 1024 / 1024, 2); ?> MB</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="actions">
                <form method="post" style="display: inline;">
                    <input type="hidden" name="action" value="set_webhook">
                    <button type="submit" class="btn success">🔗 Set Webhook</button>
                </form>

                <form method="post" style="display: inline;">
                    <input type="hidden" name="action" value="delete_webhook">
                    <button type="submit" class="btn danger">🗑️ Delete Webhook</button>
                </form>

                <form method="post" style="display: inline;">
                    <input type="hidden" name="action" value="test_bot">
                    <button type="submit" class="btn">🧪 Test Bot</button>
                </form>

                <form method="post" style="display: inline;">
                    <input type="hidden" name="action" value="maintenance">
                    <button type="submit" class="btn warning">🔧 Run Maintenance</button>
                </form>

                <a href="webhook.php" class="btn" target="_blank">📡 View Webhook</a>
            </div>
        </div>
    </div>
</body>
</html>
