<?php
/**
 * Legacy Telegram Bot - DEPRECATED
 * This file has been refactored into a modular structure.
 * Please use the new webhook.php file instead.
 */

// Redirect to new webhook
if (file_exists('public/webhook.php')) {
    header('Location: public/webhook.php');
    exit;
}

// Fallback for backward compatibility
require_once 'config/config.php';
require_once 'src/Bot.php';

try {
    $bot = new TelegramBot\Bot();
    $bot->handleWebhook();
} catch (Exception $e) {
    error_log("Bot Error: " . $e->getMessage());
}


# Short
$update = json_decode(file_get_contents("php://input"));
$message = $update->message;
$txt = $message->caption;
$text = $message->text;
$chat_id = $message->chat->id;
$from_id = $message->from->id;
$new = $message->new_chat_members;
$message_id = $message->message_id;
$type = $message->chat->type;
$name = $message->from->first_name;
if(isset($update->callback_query)){
$callbackMessage = '';
var_dump(bot('answerCallbackQuery',[
'callback_query_id'=>$update->callback_query->id,
'text'=>$callbackMessage]));
$up = $update->callback_query;
$chat_id = $up->message->chat->id;
$from_id = $up->from->id;
$user = $up->from->username;
$name = $up->from->first_name;
$message_id = $up->message->message_id;
$data = $up->data;
}
$id = $update->inline_query->from->id;
$sud = file_get_contents("admin.txt");

$sudo = array("826813929","826813929","826813929","$sudo","826813929");
//$wathq1 = $sud;
$wathq1 = 826813929;
mkdir("sudo");
mkdir("message");
mkdir("data");
mkdir("ms");
mkdir("count");
mkdir("count/user");
mkdir("count/admin");
$START= file_get_contents("data/start.txt");
$getmes_id = explode("\n", file_get_contents("ms/$message_id.txt"));
$get_ban=file_get_contents('data/ban.txt');
$ban =explode("\n",$get_ban);
/////////////////////

$member = explode("\n",file_get_contents("sudo/member.txt"));
$cunte = count($member)-1;
//////////

$amr = file_get_contents("sudo/amr.txt");
$ch1 = file_get_contents("sudo/ch1.txt");
$ch2= file_get_contents("sudo/ch2.txt");
$taf3il = file_get_contents("sudo/tanbih.txt");
$estgbal = file_get_contents("sudo/estgbal.txt");

 //826813929
$reply = $message->reply_to_message->message_id;
$rep = $message->reply_to_message->forward_from;

////======================\\\\

if($message){
$join = file_get_contents("https://api.telegram.org/bot$token/getChatMember?chat_id=$ch1&user_id=$from_id");
$join2 = file_get_contents("https://api.telegram.org/bot$token/getChatMember?chat_id=$ch2&user_id=$from_id");

if($message && (
strpos($join,'"status":"left"')
or
strpos($join,'"Bad Request: USER_ID_INVALID"')
or
strpos($join,'"Bad Request: user not found"')
or
strpos($join,'"ok": false')
or strpos($join,'"status":"kicked"') or
strpos($join2,'"status":"left"')
or
strpos($join2,'"Bad Request: USER_ID_INVALID"')
or
strpos($join2,'"Bad Request: user not found"')
or
strpos($join2,'"ok": false')
or strpos($join2,'"status":"kicked"'))!== false){
$ch1=str_replace("@","",$ch1);
$ch2=str_replace("@","",$ch2);
bot('sendMessage',[
'chat_id'=>$chat_id,
'reply_to_message_id'=>$message_id,
'text'=>"*• عذراً يجب عليك الاشتراك في القناه
 لتستطيع استخدام البوت ⚠️ 👇🏻

• اشترك ❗️ثم أرسل /start《 ✔️ 》*",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>"قناة البوت", 'url'=>"https://t.me/$ch1"]],
]
])
]);
return false;}}





if($update and !in_array($from_id, $member)){
file_put_contents("sudo/member.txt","$from_id\n",FILE_APPEND);

if($taf3il == "yas" ){
bot("sendmessage",[
"chat_id"=>$wathq1,
"text"=>"٭ تم دخول شخص جديد الى البوت الخاص بك 👾
-----------------------
• معلومات العضو الجديد .

• الاسم : [$name](tg://user?id=$from_id)
• الايدي : $from_id
-----------------------
• عدد الاعضاء الكلي : $cunte",
'disable_web_page_preview'=>'true',
'parse_mode'=>"markdown",
]);
}else{
bot("sendmessage",[
"chat_id"=>$wathq1,
"text"=>"",
]);
}}
if($data=="admin"and in_array($from_id, $sudo)){
bot('EditMessageText',[
'chat_id'=>$chat_id,
'message_id'=>$message_id,
"text"=>"*• اهلا بك في لوحه الأدمن الخاصه بالبوت 🤖

- يمكنك التحكم في البوت الخاص بك من هن*ا",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>"اخر تحديثات البوت 🧬","url"=>"t.me/VEVoGamez"]],
[['text'=>'عمل البوت' ,'callback_data'=>"estgbal"],['text'=>'اشعارات الدخول : ✅' ,'callback_data'=>"tnbeh"]],
[['text'=>'قسم الاشتراك الاجباري' ,'callback_data'=>"ula1"]],
[['text'=>'قسم الاذاعة' ,'callback_data'=>"ethaa"]],
[['text'=>'قسم الاحصائيات' ,'callback_data'=>"memb"]],
[['text'=>'قسم الادمنية' ,'callback_data'=>"trkeaa"]],
   ]
   ])
]);
unlink("sudo/amr.txt");

}
if($text=="/start"and in_array($from_id, $sudo)){
bot('sendmessage',[
'chat_id'=>$chat_id,
'message_id'=>$message_id,
"text"=>"*• اهلا بك في لوحه الأدمن الخاصه بالبوت 🤖

- يمكنك التحكم في البوت الخاص بك من هنا*",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>"اخر تحديثات البوت 🧬","url"=>"t.me/VEVoGamez"]],
[['text'=>'عمل البوت' ,'callback_data'=>"estgbal"],['text'=>'اشعارات الدخول : ✅' ,'callback_data'=>"tnbeh"]],
[['text'=>'قسم الاشتراك الاجباري' ,'callback_data'=>"ula1"]],
[['text'=>'قسم الاذاعة' ,'callback_data'=>"ethaa"]],
[['text'=>'قسم الاحصائيات' ,'callback_data'=>"memb"]],
[['text'=>'قسم الادمنية' ,'callback_data'=>"trkeaa"]],
   ]
   ])
]);
unlink("sudo/amr.txt");

}
///====قنوات الاشتراك الاجباري ====\\\\


if($data == "ulfa"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- أوامر قناة الإشتراك الإجباري الأولى 📡1⃣".',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'- وضع قناة 📡✅".' ,'callback_data'=>"ch1"]],
[['text'=>'- حذف القناة 📡❎".' ,'callback_data'=>"dch1"]],[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]
])
]);
}
if($data == "ula1"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'*• مرحبا بك في قسم الاشتراك الاجباري 💫*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'تعين القناة' ,'callback_data'=>"ch1"],['text'=>'مسح القناة' ,'callback_data'=>"hhhx"]],
[['text'=>'عرض قنوات الاشتراك الاجباري' ,'callback_data'=>"gnoaty"]],
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]
])
]);
}

if($data == "ch1"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'*• قم برفع البوت ادمن في قناتك اولاً 🌟

• من ثم ارسال معرف القناة الى البوت*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"ula1"]],
]])
]);
file_put_contents("sudo/amr.txt","ch1");
}
if($text and $amr == "ch1" and $from_id == $wathq1){
bot("sendmessage",[
"chat_id"=>$chat_id,
"text"=>'*تم حفظ القناة  '.$ch1.'.*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"ula1"]],
]])
]);
file_put_contents("sudo/ch1.txt","$text");
unlink("sudo/amr.txt");
}
if($data == "hhx"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'*تم حذف القناة*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"ula1"]],
]])
]);
unlink("sudo/amr.txt");
unlink("sudo/ch1.txt");
}
/////////////////2/////////////////
if($data == "thany"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- أوامر قناة الإشتراك الإجباري الثانية 📢2⃣".',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'- وضع قناة 📡✅".' ,'callback_data'=>"ch2"]],
[['text'=>'- حذف القناة 📡❎".' ,'callback_data'=>"dch2"]],[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]
])
]);
}
if($data == "ch2"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- أرسل معرف القناة الآن Ⓜ️".',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"thany"]],
]])
]);
file_put_contents("sudo/amr.txt","ch2");
}
if($text and $amr == "ch2" and $from_id == $wathq1){
bot("sendmessage",[
"chat_id"=>$chat_id,
"text"=>'- تم حفظ معرف القناة الثانية بنجاح ✅".

- تأكد من أن البوت أدمن في القناة ليتم تفعيل الإشتراك الإجباري 👨🏻‍✈️".',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]])
]);
file_put_contents("sudo/ch2.txt","$text");
unlink("sudo/amr.txt");
}
if($data == "dch2"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- تم حذف القناة بنجاح ✅".',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]])
]);
unlink("sudo/amr.txt");
unlink("sudo/ch2.txt");
}


///////////////3/////////////////
if($data == "gnoaty"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'*قائمة قنوات الإشتراك الإجباري :

'.$ch1.'*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"ula1"]],
]])
]);
unlink("sudo/amr.txt");
}

///==== الاذاعة  ====\\\\
$memberi = explode("\n",file_get_contents("sudo/member.txt"));
$cuntei = count($memberi)-1;

if($data == "ethaa"){
$mim= file_get_contents("sudo/member.txt");
file_put_contents("sudo/mim.txt","$mim");
mkdir("ms");
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'•* مرحبا بك في قسم الاذاعه 🔥*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'اذاعة توجيه' ,'callback_data'=>"tawgih"]],
[['text'=>'html اذاعة' ,'callback_data'=>"messageh"],['text'=>'اذاعة ماركـدون' ,'callback_data'=>"messagem"]],
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]
])
]);
}


$miim = explode("\n",file_get_contents("sudo/mim.txt"));
$cmiim = count($miim)-1;
/////////////////markdown
if($data == "messagem"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>"• ارسال الان الكليشه ( نص او جميع الميديا )
• يمكنك استخدام كود جاهز في الاذاعه او يمكنك استخدام الماركداون",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"ethaa"]],
]])
]);
file_put_contents("sudo/amr.txt","messagemark");
}

if($text and $amr == "messagemark" and in_array($from_id, $sudo)){
unlink("sudo/amr.txt");
bot("sendMessage",[
"chat_id"=>$chat_id,
"text"=>"$text",
"message_id"=>$message_id+1,
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true
]);
$msg = $message_id+1;
for($i=0;$i<count($miim);$i++){
$get =bot('SendMessage', [
'chat_id' => $miim[$i],
'text'=>$text,
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true
]);
$msg_id = $get->result->message_id;
file_put_contents("ms/$msg.txt",$miim[$i]."==". $msg_id."\n", FILE_APPEND);
$str= file_get_contents("sudo/mim.txt");
$str= str_replace($miim[$i]."\n","",$str);
file_put_contents("sudo/mim.txt",$str);
}
$ms=explode("\n",file_get_contents("ms/$msg.txt"));
$count= count($ms);

bot("sendMessage",[
"chat_id"=>$chat_id,
"text"=>"*• تم الاذاعة بنجاح 🎉*",

"message_id"=>$message_id+1,
'reply_markup'=>json_encode([
'inline_keyboard'=>[
'parse_mode'=>"markdown",
[['text'=>"حذف الرسالة",'callback_data'=>"delelink ".$msg]],
[['text'=>'• رجوع •' ,'callback_data'=>"ethaa"]],
]])
]);
}

//////////////html
if($data == "messageh"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'*- • ارسال الان الكليشه*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"ethaa"]],
]])
]);
file_put_contents("sudo/amr.txt","messagehtml");
}
if($text and $amr == "messagehtml" and in_array($from_id, $sudo)){
unlink("sudo/amr.txt");
bot("sendMessage",[
"chat_id"=>$chat_id,
"text"=>"$text",
"message_id"=>$message_id+1,
]);
$msg = $message_id+1;
for($i=0;$i<count($miim);$i++){
$get =bot('SendMessage', [
'chat_id' => $miim[$i],
'text'=>$text,
'parse_mode'=>"html",
'disable_web_page_preview'=>true
]);
$msg_id = $get->result->message_id;
file_put_contents("ms/$msg.txt",$miim[$i]."==". $msg_id."\n", FILE_APPEND);
$str= file_get_contents("sudo/mim.txt");
$str= str_replace($miim[$i]."\n","",$str);
file_put_contents("sudo/mim.txt",$str);
}
$ms=explode("\n",file_get_contents("ms/$msg.txt"));
$count= count($ms);

bot("sendMessage",[
"chat_id"=>$chat_id,
"text"=>"• تم الاذاعة بنجاح 🎉",
'parse_mode'=>"html",
'disable_web_page_preview'=>true,
"message_id"=>$message_id+1,
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>"حذف الرسالة",'callback_data'=>"delelink ".$msg]],
[['text'=>'• رجوع •' ,'callback_data'=>"ethaa"]],
]])
]);
}
////////////توجية
if($data == "tawgih"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'*• ارسال الان الكليشه !*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"ethaa"]],
]])
]);
file_put_contents("sudo/amr.txt","tawgih");
}
if($message and $amr == "tawgih" and in_array($from_id, $sudo)){
unlink("sudo/amr.txt");
bot('ForwardMessage',[
	'chat_id'=>$chat_id,
	'from_chat_id'=>$chat_id,
"message_id"=>$message_id+1,
]);

bot("sendMessage",[
"chat_id"=>$chat_id,
"text"=>"• تم الاذاعة بنجاح 🎉",
]);
$msg = $message_id+1;
for($i=0;$i<count($miim);$i++){
$get =bot('ForwardMessage',[
	'chat_id'=>$miim[$i],
	'from_chat_id'=>$chat_id,
	'message_id'=>$message->message_id,
]);
$msg_id = $get->result->message_id;
file_put_contents("ms/$msg.txt",$miim[$i]."==". $msg_id."\n", FILE_APPEND);
$str= file_get_contents("sudo/mim.txt");
$str= str_replace($miim[$i]."\n","",$str);
file_put_contents("sudo/mim.txt",$str);
}
$ms=explode("\n",file_get_contents("ms/$msg.txt"));
$count= count($ms);
unlink("sudo/amr.txt");
bot("sendMessage",[
"chat_id"=>$chat_id,
"text"=>"تم نشر التوجية بنجاح
لعدد ($count) عضو. *
",
"message_id"=>$message_id+1,
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>"حذف الرسالة",'callback_data'=>"delelink ".$msg]],
[['text'=>'• رجوع •' ,'callback_data'=>"ethaa"]],
]])
]);
}

if (preg_match('/^(delelink) (.*)/s',$data) ) {
  $data1 = explode(" ",$data);
  $mesg= $data1['1'];
  $getmes_id = explode("\n", file_get_contents("ms/$mesg.txt"));

bot('editmessagetext',[
'chat_id'=>$chat_id,
'text'=>"سيتم حذف رسالتك انتظر قليلاً",
"message_id"=>$message_id,
]);

for($d=0;$d<count($getmes_id);$d++){
$ex = explode("==", $getmes_id[$d]);
$getmes_id1=$ex['1'];
$getids1=$ex['0'];
file_get_contents("https://api.telegram.org/bot$token/deleteMessage?chat_id=$getids1&message_id=$getmes_id1");
}
unlink("ms/$message_id.txt");
bot('editmessagetext',[
'chat_id'=>$chat_id,
'text'=>"تم حذف رسالتك لدى كل الاعضاء ✅",
"message_id"=>$message_id,
]);
}

///==== عدد آعضاء  ====\\\\

if($data == "memb"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- مرحبا بك في قسم الاحصائيات 📊

• عدد المسخدمين الكلي : '.$cuntei.'.',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]])
]);
unlink("sudo/amr.txt");
}
$getben = explode("\n",file_get_contents("data/ban.txt"));
$countben = count($getben)-1;
if($data == 'ban'){
bot('answerCallbackQuery',[
'callback_query_id'=>$update->callback_query->id,
'message_id'=>$message_id,
'text'=>'عدد 💎 المحضورين ❌ : ' . $countben,
 'show_alert'=>true
 ]);
}


if($data == "tnbeh"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'قسم الاشعارت',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'- تفعيل التنبيه' ,'callback_data'=>"tf3il"],['text'=>'- تعطيل التنبيه' ,'callback_data'=>"tatil"]]
,[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]
])
]);
}
if($data == "trkeaa"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'• مرحبا بك في الادمنيه 👮‍♀️
- يمكنك رفع 5 ادمنيه في البوت او حذفهم

- يمكن للادمنيه تحكم في لوحه البوت مثلك ولا يمكنهم رفع ادمنيه او استلام رسائل الموجهة او سايت او تواصل .',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'•اضافة ادمن •' ,'callback_data'=>"rf3admin"],['text'=>'تنزيل ادمن' ,'callback_data'=>"tnzeil"]],
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]
])
]);
}
if($data == "rf3admin"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'اهلا عزيزي المطور 🤍👋🏼.
 لرفع ادمن قم بارسال كلمة ( `ترقية` )',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"trkeaa"]],
]
])
]);
}
if($data == "tnzeil"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'*لتنزيل ادمن قم بارسال `تنزيل` معا ايدي او معرف الادمن *',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"trkeaa"]],
]
])
]);
}
if($data == "hhhx"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'*قسم حذف الاشتراك الاجباري

'.$ch1.'*',
'parse_mode'=>"markdown",
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'- حذف القناة' ,'callback_data'=>"hhx"]],
[['text'=>'• رجوع •' ,'callback_data'=>"ula1"]],
]
])
]);
}

if($data == "tf3il"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>' تم تفعيل التنبيه - ✅',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]])
]);
file_put_contents("sudo/tanbih.txt","yas");
}

if($data == "tatil"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>' تم تعطيل التنبيه - ❌',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]])
]);
unlink("sudo/tanbih.txt");
}

/////////////////7////////////////
if($data == "estgbal"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'• مرحبا بك في قسم عمل البوت 🥷🏾',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'- تفعيل التواصل ✅' ,'callback_data'=>"estgbalon"]],
[['text'=>'- تعطيل التواصل ❌' ,'callback_data'=>"estgbaloff"]],
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]
])
]);
}

if($data == "typee"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- تم التفعيل سيتم ايصال الرسائل الى الخاص 🔃✅".',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"admin"]],
]])
]);
file_put_contents("sudo/typee.txt","$from_id");
}


if($data == "supergruope"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'حسننا عزيزي الادمن قم بالذهاب الى قروب الاستقبال وارسل امر
/setastgbal
📝".',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"ethaa"]],
]])
]);
file_put_contents("sudo/amr.txt","set");
}
if($text=="/setastgbal" and $amr == "set" and in_array($from_id, $sudo)){
file_put_contents("sudo/amr.txt","");
bot('sendmessage',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- تم تحديد هذا القروب ليكون قروبا للاستقبال ".',
]);
file_put_contents("sudo/typee.txt","$chat_id");
}
if($data == "estgbalon"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- تم تفعيل التواصل ✅ .',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"estgbal"]],
]])
]);
file_put_contents("sudo/estgbal.txt","on");
}

if($data == "estgbaloff"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'- تم تعطيل التواصل ❌ .',
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"estgbal"]],
]])
]);
unlink("sudo/amr.txt");
unlink("sudo/estgbal.txt");
}


if($data == 'atther'){

bot('editMessageText',[
'chat_id'=>$chat_id,
'message_id'=>$message_id,
'text'=>'⚙️هلاو مطور هذا القسم الخاص بل التعديل على المعلومات التي تضها في البوت',
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[
['text'=>'اضافت ترحيب 🗒','callback_data'=>'welc']],

[
['text'=>'اضافه رساله الرد','callback_data'=>'msrd']
],

[
['text'=>'رجوع الى الاوامر','callback_data'=>'admin']
]
]
])
]);
}
if($data == 'welc'){
bot('editMessageText',[
'chat_id'=>$chat_id,
'message_id'=>$message_id,
'text'=>'ارسل الترحيب الان 🗒',
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>'الغاء ❌','callback_data'=>'atther']]
]
])
]);
file_put_contents("sudo/amr.txt","start");
}


if($message and $amr == "start" and in_array($from_id, $sudo)){
unlink("sudo/amr.txt");
bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"تم حفظ الترحيب للاعضاء الجدد
الترحيب هو :
$text
",
]);
file_put_contents("data/start.txt","$text");
}








if($data == 'msrd'){
bot('editMessageText',[
'chat_id'=>$chat_id,
'message_id'=>$message_id,
'text'=>"قم بتحديد رسالة الرد على العضو بعد ان يقوم بارسال رساله لك ....
",
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>'الغاء ❌','callback_data'=>'atther']]
]
])
]);

file_put_contents("sudo/amr.txt","msrd");
}

if($message and $amr == "msrd" and in_array($from_id, $sudo)){
unlink("sudo/amr.txt");
bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"تم حفظ رسالة الرد
رسالة الرد هي  :
$text
",
]);

file_put_contents("data/msrd.txt","$text");
}





$yppee=file_get_contents("sudo/typee.txt");
if($yppee==null or $yppee==""){
$yppee=$wathq1;

}
$get_ban=file_get_contents('data/ban.txt');
$ban =explode("\n",$get_ban);

if($text=="ترقية" and in_array($from_id,$sudo)
){

$rand=rand(111111,999999);
bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"اهلا عزيزي المطور لرفع اي شخص ليكون ادمن قم بارسال هذا الرمز له
رمز الترقية : `$rand`
",
'reply_to_message_id'=>$message_id
,
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
]);
file_put_contents("data/trgih.txt","$rand");
}
$adminall=explode("\n",file_get_contents("sudo/admin.txt"));
$amrtr=file_get_contents("data/$from_id.txt");
if($text=="ارفعني" and $from_id!=$wathq1 and !in_array($from_id, $sudo) ){
if(!in_array($from_id, $ban)){
if(!in_array($from_id, $adminall)){
bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"اهلا بك عزيزي
اذا كنت تريد ان يقوم البوت برفعك ادمن قم بارسال رمز الترقيه المكون من 6 ارقام
",
'reply_to_message_id'=>$message_id
,
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
]);
file_put_contents("data/$from_id.txt","yes");
}else{
bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"لقد تمت ترقيتك بالفعل مسبقا   ✅
",
'reply_to_message_id'=>$message_id
,
]);
}
}else{
bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"❌ المعذرة لا استطيع ترقيتك لانك محظور من قبل مالك البوت
",
'reply_to_message_id'=>$message_id
,
]);
}
}


$randtrg=file_get_contents("data/trgih.txt");
if($text and !$data and $amrtr == "yes" ){
unlink("data/$from_id.txt");


if($text==$randtrg){

if(!in_array($from_id, $adminall)){
file_put_contents("sudo/admin.txt","$from_id\n",FILE_APPEND);
}
bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"لقد تمت ترقيتك بنجاح عزيزي ✅
",
]);
bot('sendmessage',[
	'chat_id'=>$yppee,
'text'=>"✅
 لقد تمت ترقية  العضو
 - [$name](tg://user?id=$from_id)
",
]);
}else{
bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"عذرا عزيزي رمز الترقية خاطئ❌",
]);
}
}
if($text=="الادمنية" and $from_id == $wathq1
){
unlink("sudo/admins.txt");
for($h=0;$h<count($adminall);$h++){
if($adminall[$h] != ""){
$admin = json_decode(file_get_contents("http://api.telegram.org/bot$token/getChat?chat_id=$adminall[$h]"))->result;
$from=$adminall[$h];
$name= $admin->first_name;

$admins="- [$name](tg://user?id=$from) `$from`";
file_put_contents("sudo/admins.txt","$admins\n",FILE_APPEND);

}}

$admins=file_get_contents("sudo/admins.txt");


bot('sendmessage',[
	'chat_id'=>$chat_id,
'text'=>"ℹ هذة هي قائمة الادمنية
$admins
",
'reply_to_message_id'=>$message_id
,
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
]);

}
if (preg_match('/^(تنزيل) (.*)/s',$text) and $from_id == $wathq1
)
{
$textt = str_replace("تنزيل ","",$text);

$strlen=strlen($textt);
if($strlen < 12){

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>' تم ✅ تنزيل العضو من الادمنية ❌',
'reply_to_message_id'=>$message_id
]);

bot('sendMessage',[
'chat_id'=>$textt,
'text'=>'تم ✅  تنزيلك من الا دمنية ❌'
]);
$bin=file_get_contents('sudo/admin.txt');
$str = str_replace($textt."\n", '' ,$bin);

file_put_contents('sudo/admin.txt', $str);

}
}



///====  الاعضاء  ====\\\\
$start= file_get_contents("data/start.txt");
$ainfo= file_get_contents("data/ainfo.txt");
$chan= file_get_contents("data/chan.txt");
$law= file_get_contents("data/law.txt");
$infobot= file_get_contents("data/infobot.txt");
$msrd= file_get_contents("data/msrd.txt");



if($text == '/start' and !in_array($from_id,$ban) and $message->chat->type == 'private' and $chat_id != $sudo ){
$user = $update->message->from->username;
if($user){
bot('sendphoto',[
'chat_id'=>$chat_id,
'photo'=>"https://t.me/vevogamerz",
'caption'=>"*اهلا بك عزيزي 🤍.
البوت 🤖 مخصص للتواصل مع فريق VEVoGamez. يمكنكم التواصل وطرح الأسئلة، كما يمكنكم طلب لعبة أو تطبيق. ❗️

ارسل رسالتك وانتظر الرد.!

Dev - @VEVoGamez*",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"قناة البوت","url"=>"t.me/VEVoGamez"],['text'=>"Yooz Dev","url"=>"t.me/GoogleYooz"]]
]
])
]);
}else{
bot('sendmessage',[
'chat_id'=>$chat_id,
'text'=>"*اهلا بك عزيزي 🤍.
البوت 🤖 مخصص للتواصل مع فريق VEVoGamez. يمكنكم التواصل وطرح الأسئلة، كما يمكنكم طلب لعبة أو تطبيق. ❗️

ارسل رسالتك وانتظر الرد.!

Dev - @VEVoGamez*",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"قناة البوت","url"=>"t.me/VEVoGamez"],['text'=>"Yooz Dev","url"=>"t.me/GoogleYooz"]]
]
])
]);
}
}

$yppee=file_get_contents("sudo/typee.txt");
if($yppee==null or $yppee==""){
$yppee=$wathq1;

}

$co_m_all=file_get_contents("count/user/all.txt");
$co1=$co_m_all+1;
$repp=$message->reply_to_message->message_id-1;
$msg=explode("=",file_get_contents("message/$repp.txt"));
$get_ban=file_get_contents('data/ban.txt');
$get_ban=file_get_contents('data/ban.txt');
$ban =explode("\n",$get_ban);
if($text){

if($text != '/start' and $text != 'جهة اتصال المدير☎️' and $text != '⚜️〽️┇قناه البوت' and $text != 'ارفعني' and $text != 'القوانين ⚠️' and $text != 'معلومات المدير 📋' and   $text !='المساعده 💡' and $text !='اطلب بوتك من المطور' and $message->chat->type == 'private' and $from_id != $wathq1 ){
if(!in_array($from_id, $ban)){
if($estgbal=="on" or $estgbal==null){
bot('sendMessage',[
'chat_id'=>$yppee,
'text'=>"رساله من : [$name](tg://user?id=$from_id)
",
'reply_to_message_id'=>$message->reply_to_message->message_id-1,
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,

]);

$get= bot('forwardMessage',[
'chat_id'=>$yppee,
'from_chat_id'=>$chat_id,
'message_id'=>$message_id,

]);
$msg_id = $get->result->message_id-1;

$from_id_name="$chat_id=$name=$message_id";
file_put_contents("message/$msg_id.txt","$from_id_name");

$co_m_us=file_get_contents("count/user/$from_id.txt");
$co=$co_m_us+1;
file_put_contents("count/user/$from_id.txt","$co");
file_put_contents("count/user/all.txt","$co1");

if($msrd !=null){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"$msrd ــــ ",
'reply_to_message_id'=>$message_id
]);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>" ",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"أشترك في القناة ليصلك التحديثات","url"=>"t.me/VEVoGamez"]]

]
])
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"تم ايقاف استقبال الرسائل من قبل مالك البوت ",
'reply_to_message_id'=>$message_id
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"❌ المعذرة لا تستطيع ارسال الرسائل انت محظور ",
'reply_to_message_id'=>$message_id
]);
}
}}

$photo=$message->photo;
$video=$message->video;
$document=$message->document;
$sticker=$message->sticker;
$voice=$message->voice;
$audio=$message->audio;

$n_id =$msg['1'];
if($reply and $text != 'الغاء الحظر' and $text != 'حظر' and $text != 'معلومات' and $chat_id == $yppee
and $n_id!= null){
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

if($text){
$get=bot('sendMessage',[
'chat_id'=>$msg['0'],
'text'=>$text,
'reply_to_message_id'=>$msg['2'],

]);
$msg_id = $get->result->message_id;
}else{

if($photo){
$sens="sendphoto";
$file_id = $update->message->photo[1]->file_id;
}
if($document){
$sens="senddocument";
$file_id = $update->message->document->file_id;
}
if($video){
$sens="sendvideo";
$file_id = $update->message->video->file_id;
}

if($audio){
$sens="sendaudio";
$file_id = $update->message->audio->file_id;
}

if($voice){
$sens="sendvoice";
$file_id = $update->message->voice->file_id;
}

if($sticker){
$sens="sendsticker";
$file_id = $update->message->sticker->file_id;
}

$ss=str_replace("send","",$sens);
$get1=bot($sens,[
"chat_id"=>$msg['0'],
"$ss"=>"$file_id",
'caption'=>"$getfull",
'reply_to_message_id'=>$msg['2'],
]);

$msg_id = $get->result->message_id;
}

$ch_id =$msg['0'];
$name_id =$msg['1'];
$wathqid="$ch_id=$msg_id=$name_id";
file_put_contents("message/$msg_id.txt","$wathqid");

$co_m_ad=file_get_contents("count/admin/$ch_id.txt");
$co=$co_m_ad+1;
file_put_contents("count/admin/$ch_id.txt","$co");

$co_m_all=file_get_contents("count/admin/all.txt");
$coo=$co_m_all+1;
file_put_contents("count/admin/all.txt","$coo");
bot('sendmessage',[
'chat_id'=>$yppee,
'text'=>"
◾ تم الارسال لـ [$name_id](tg://user?id=$ch_id) بنجاح .
",
'reply_to_message_id'=>$message_id
,'parse_mode'=>"MarkDown",

'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>"◾ تعديل الرسالة .",'callback_data'=>"edit_msg ".$msg_id],
['text'=>"◾ حذف الرسالة .",'callback_data'=>"del_msg ".$msg_id]],
]
])
]);
}}

if (preg_match('/^(del_msg) (.*)/s',$data) ) {
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

  $data1 = explode(" ",$data);
  $wathqid= $data1['1'];
$info=explode("=",file_get_contents("message/$wathqid.txt"));

  $ch_id= $info['0'];
  $msg_id= $info['1'];
  $name_id =$info['2'];
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>"
◾ تم حذف الرسالة لـ [$name_id](tg://user?id=$ch_id) بنجاح

",
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
]);
bot('deleteMessage',[
'chat_id'=>$ch_id,
'message_id'=>$msg_id,
]);
  }
}
if (preg_match('/^(edit_msg) (.*)/s',$data) ) {
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

$data1 = explode(" ",$data);
  $wathqid= $data1['1'];
$info=explode("=",file_get_contents("message/$wathqid.txt"));

  $ch_id= $info['0'];
  $msg_id= $info['1'];
  $name_id =$info['2'];
  file_put_contents("data/t3dil.txt","$ch_id=$msg_id=$name_id");
bot('sendmessage',[
'chat_id'=>$chat_id,
'text'=>"
◾ قم بارسال رسالتك الجديده ليتم تعديل الرسالة السابقه لدى [$name_id](tg://user?id=$ch_id)  .
",
'reply_to_message_id'=>$message_id
,'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
 'reply_markup'=>json_encode([
      'inline_keyboard'=>[
[['text'=>'• رجوع •' ,'callback_data'=>"trag3"]],
]])
]);
file_put_contents("sudo/amr.txt","edit");
  }
}
if($data == "trag3"){
bot('EditMessageText',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>'تم الغاء التعديل بنجاح".',
]);
file_put_contents("sudo/amr.txt","");
file_put_contents("data/t3dil.txt","");
}
if($text and $amr == "edit" and $chat_id== $yppee){
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

file_put_contents("sudo/amr.txt","");
$wathqget=file_get_contents("data/t3dil.txt");

  $wathqidd = explode("=",$wathqget);
  $ch_id= $wathqidd['0'];
  $msg_id= $wathqidd['1'];
  $name_id =$wathqidd['2'];
  bot('deleteMessage',[
'chat_id'=>$chat_id,
'message_id'=>$message_id-2,
]);
bot('deleteMessage',[
'chat_id'=>$chat_id,
'message_id'=>$message_id-1,
]);

bot('sendmessage',[
    'chat_id'=>$chat_id,
    'message_id'=>$message_id,
'text'=>"- ✅ تم التعديل الرسالة لـ [$name_id](tg://user?id=$ch_id) بنجاح .",
'reply_to_message_id'=>$message_id
,'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>"◾ تعديل الرسالة .",'callback_data'=>"edit_msg ".$msg_id],
['text'=>"◾ حذف الرسالة .",'callback_data'=>"del_msg ".$msg_id]],
]
])
]);
file_put_contents("data/t3dil.txt","");
bot('EditMessageText',[
    'chat_id'=>$ch_id,
    'message_id'=>$msg_id,
    'text'=>
$text,
]);
}}

if (preg_match('/^(حظر) (.*)/s',$text) and $chat_id == $yppee ) {
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

$textt = str_replace("حظر ","",$text);
$textt = str_replace(" ","",$text);
$strlen=strlen($textt);
if($strlen < 10){
if(!in_array($textt, $ban)){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>'تم ✅ حضر العضو 🚹',
'reply_to_message_id'=>$message_id
]);

bot('sendMessage',[
'chat_id'=>$textt,
'text'=>'تم ✅ حضرك من البوت ❌',
]);
file_put_contents('data/ban.txt', $textt. "\n", FILE_APPEND);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>' العضو محظور مسبقا🚫 🚹',
'reply_to_message_id'=>$message_id
]);
}
}}
}
if (preg_match('/^(الغاء حظر) (.*)/s',$text) and $chat_id == $yppee ) {
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

$textt = str_replace("الغاء حظر ","",$text);
$textt = str_replace(" ","",$text);
$strlen=strlen($textt);
if($strlen < 10){
if(in_array($textt, $ban)){

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>'تم ✅ الغاء حضر العضو ❌',
'reply_to_message_id'=>$message_id
]);

bot('sendMessage',[
'chat_id'=>$textt,
'text'=>'تم ✅ الغاء حضرك ❌'
]);
$bin=file_get_contents('data/ban.txt');
$str = str_replace($textt."\n", '' ,$bin);

file_put_contents('data/ban.txt', $str);

}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>' 🚫 العضو ليس محظور 🚹',
'reply_to_message_id'=>$message_id
]);
}}}
}

if($reply and $text == 'حظر' and $chat_id == $yppee  and $n_id!= null){
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

//$from = $message->reply_to_message->forward_from->id;
$from = $msg['0'];
if(!in_array($from, $ban)){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>'تم ✅ حضر العضو 🚹',
'reply_to_message_id'=>$message_id
]);

bot('sendMessage',[
'chat_id'=>$from,
'text'=>'تم ✅ حضرك من البوت ❌',
]);

file_put_contents('data/ban.txt', $from. "\n", FILE_APPEND);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>' العضو محظور مسبقا🚫 🚹',
'reply_to_message_id'=>$message_id
]);
}}
}

if($reply and $text == 'الغاء الحظر' and $chat_id == $yppee and $n_id!= null){
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

//$from = $message->reply_to_message->forward_from->id;
$from = $msg['0'];
if(in_array($from, $ban)){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>'تم ✅ الغاء حضر العضو ❌',
'reply_to_message_id'=>$message_id
]);

bot('sendMessage',[
'chat_id'=>$from,
'text'=>'تم ✅ الغاء حضرك ❌'
]);

$bin=file_get_contents('data/ban.txt');
$str = str_replace($from ."\n", '' ,$bin);

file_put_contents('data/ban.txt', $str);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>' 🚫 العضو ليس محظور 🚹',
'reply_to_message_id'=>$message_id,
]);
}
}
}
//معلومات الاعضاء
if($reply and $text == 'معلومات' and $chat_id == $yppee){
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

//$from = $message->reply_to_message->forward_from->id;
$from = $msg['0'];
$admin = json_decode(file_get_contents("http://api.telegram.org/bot$token/getChat?chat_id=$from"))->result;
$name= $admin->first_name;
$user= $admin->username;

if(!in_array($from, $ban)){

$info="✅ متفاعل";
}else{
$info="🚫 محظور";
}
$co_m_us=file_get_contents("count/user/$from.txt");
$co_m_ad=file_get_contents("count/admin/$from.txt");

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"ℹ معلومات العضو
~~~~~~~~~~~~~~~~~~~~~~~
- الاسم : [ $name](tg://user?id=$from)  .
- الايدي : `$from`
- المعرف : *@$user*
- حالة العضو : $info
- عدد الرسائل المستلمة منة : $co_m_us ✉
- عدد الرسائل المرسلة لة : $co_m_ad 📬

",
'reply_to_message_id'=>$message_id,
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
]);
}
}
if( $text == 'معلومات' and !$reply  and $chat_id == $yppee){
if( in_array($from_id,$sudo)
or in_array($from_id, $adminall)){

unlink("sudo/admins.txt");
for($h=0;$h<count($adminall);$h++){
if($adminall[$h] != ""){
$admin = json_decode(file_get_contents("http://api.telegram.org/bot$token/getChat?chat_id=$adminall[$h]"))->result;
$from=$adminall[$h];
$name= $admin->first_name;
$c= $h+1;
$admins="$c - [$name](tg://user?id=$from) `$from`";
file_put_contents("sudo/admins.txt","$admins\n",FILE_APPEND);

}}

$admins=file_get_contents("sudo/admins.txt");


$co_m_us=file_get_contents("count/user/all.txt");
$co_m_ad=file_get_contents("count/admin/all.txt");
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"ℹ معلومات شاملة عن البوت
~~~~~~~~~~~~~~~~~~~~~~~
👮 - الادمنية :
$admins
--------------------
👪 - عدد الاعضاء : $cuntei
🚫 - المحضورين : $countben
--------------------
📮 - الرسائل
📩 - المستلمة :$co_m_us
📬 - الصادرة :$co_m_ad


",
'reply_to_message_id'=>$message_id,
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
]);
}
}
$photo=$message->photo;
$video=$message->video;
$document=$message->document;
$sticker=$message->sticker;
$voice=$message->voice;
$audio=$message->audio;
$forward =$message->forward_from_chat;
$c_photo=file_get_contents("data/photo.txt");
$c_video=file_get_contents("data/video.txt");
$c_document=file_get_contents("data/document.txt");
$c_sticker=file_get_contents("data/sticker.txt");
$c_voice=file_get_contents("data/voice.txt");
$c_audio=file_get_contents("data/audio.txt");
$c_forward =file_get_contents("data/audio.txt");
if($from_id!=$wathq1 and $message->chat->type == 'private' ){
//الصور
if($photo and ! $forward){
if($c_photo=="❌"or $c_photo== null){
if($estgbal=="on" or $estgbal==null){

bot('sendMessage',[
'chat_id'=>$yppee,
'text'=>"رساله من : [$name](tg://user?id=$from_id)
",
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
'reply_to_message_id'=>$message->reply_to_message->message_id-1,
]);

$get=bot('forwardMessage',[
'chat_id'=>$yppee,
'from_chat_id'=>$chat_id,
'message_id'=>$message_id

]);
$msg_id = $get->result->message_id-1;

$from_id_name="$chat_id=$name=$message_id";
file_put_contents("message/$msg_id.txt","$from_id_name");

$co_m_us=file_get_contents("count/user/$from_id.txt");
$co=$co_m_us+1;
file_put_contents("count/user/$from_id.txt","$co");

file_put_contents("count/user/all.txt","$co1");

if($msrd !=null){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"$msrd..",
'reply_to_message_id'=>$message_id
]);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>" ",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"أشترك في القناة ليصلك التحديثات","url"=>"t.me/VEVoGamez"]]
]
])
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"تم ايقاف استقبال الرسائل من قبل مالك البوت ",
'reply_to_message_id'=>$message_id
]);
}
}else{

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"عذرا استقبال الصور مغلق ❌",
'reply_to_message_id'=>$message_id
]);
}

}
//الفيديو
if($video and ! $forward ){
if($c_video=="❌"or $c_photo== null){
if($estgbal=="on" or $estgbal==null){
bot('sendMessage',[
'chat_id'=>$yppee,
'text'=>"رساله من : [$name](tg://user?id=$from_id)
",
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
'reply_to_message_id'=>$message->reply_to_message->message_id-1,
]);
$get=bot('forwardMessage',[
'chat_id'=>$yppee,
'from_chat_id'=>$chat_id,
'message_id'=>$message_id

]);
$msg_id = $get->result->message_id-1;

$from_id_name="$chat_id=$name=$message_id";
file_put_contents("message/$msg_id.txt","$from_id_name");

$co_m_us=file_get_contents("count/user/$from_id.txt");
$co=$co_m_us+1;
file_put_contents("count/user/$from_id.txt","$co");
file_put_contents("count/user/all.txt","$co1");


if($msrd !=null){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"$msrd",
'reply_to_message_id'=>$message_id
]);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>" ",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"أشترك في القناة ليصلك التحديثات","url"=>"t.me/VEVoGamez"]]
]
])
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"تم ايقاف استقبال الرسائل من قبل مالك البوت ",
'reply_to_message_id'=>$message_id
]);
}
}else{

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"عذرا استقبال الفيديو مغلق ❌",
'reply_to_message_id'=>$message_id
]);
}

}

//الملفات
if($document and ! $forward){
if($c_document=="❌"or $c_photo== null){
if($estgbal=="on" or $estgbal==null){
bot('sendMessage',[
'chat_id'=>$yppee,
'text'=>"رساله من : [$name](tg://user?id=$from_id)
",
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
'reply_to_message_id'=>$message->reply_to_message->message_id-1,
]);
$get=bot('forwardMessage',[
'chat_id'=>$yppee,
'from_chat_id'=>$chat_id,
'message_id'=>$message_id

]);
$msg_id = $get->result->message_id-1;

$from_id_name="$chat_id=$name=$message_id";
file_put_contents("message/$msg_id.txt","$from_id_name");

$co_m_us=file_get_contents("count/user/$from_id.txt");
$co=$co_m_us+1;
file_put_contents("count/user/$from_id.txt","$co");
file_put_contents("count/user/all.txt","$co1");

if($msrd !=null){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"$msrd",
'reply_to_message_id'=>$message_id
]);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>" ",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"أشترك في القناة ليصلك التحديثات","url"=>"t.me/VEVoGamez"]]
]
])
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"تم ايقاف استقبال الرسائل من قبل مالك البوت ",
'reply_to_message_id'=>$message_id
]);
}
}else{

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"عذرا استقبال الملفات مغلق ❌",
'reply_to_message_id'=>$message_id
]);
}

}

//الملصقات
if($sticker and ! $forward ){
if($c_sticker=="❌"or $c_photo== null){
if($estgbal=="on" or $estgbal==null){
bot('sendMessage',[
'chat_id'=>$yppee,
'text'=>"رساله من : [$name](tg://user?id=$from_id)
",
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
'reply_to_message_id'=>$message->reply_to_message->message_id-1,
]);
$get=bot('forwardMessage',[
'chat_id'=>$yppee,
'from_chat_id'=>$chat_id,
'message_id'=>$message_id

]);
$msg_id = $get->result->message_id-1;

$from_id_name="$chat_id=$name=$message_id";
file_put_contents("message/$msg_id.txt","$from_id_name");

$co_m_us=file_get_contents("count/user/$from_id.txt");
$co=$co_m_us+1;
file_put_contents("count/user/$from_id.txt","$co");
file_put_contents("count/user/all.txt","$co1");

if($msrd !=null){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"$msrd",
'reply_to_message_id'=>$message_id
]);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>" ",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"أشترك في القناة ليصلك التحديثات","url"=>"t.me/VEVoGamez"]]
]
])
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"تم ايقاف استقبال الرسائل من قبل مالك البوت ",
'reply_to_message_id'=>$message_id
]);
}
}else{

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"عذرا استقبال الملفات مغلق ❌",
'reply_to_message_id'=>$message_id
]);
}

}

//الصوتيات
if($voice and ! $forward ){
if($c_voice=="❌"or $c_photo== null){
if($estgbal=="on" or $estgbal==null){
bot('sendMessage',[
'chat_id'=>$yppee,
'text'=>"رساله من : [$name](tg://user?id=$from_id)
",
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
'reply_to_message_id'=>$message->reply_to_message->message_id-1,
]);
$get=bot('forwardMessage',[
'chat_id'=>$yppee,
'from_chat_id'=>$chat_id,
'message_id'=>$message_id

]);
$msg_id = $get->result->message_id-1;

$from_id_name="$chat_id=$name=$message_id";
file_put_contents("message/$msg_id.txt","$from_id_name");

$co_m_us=file_get_contents("count/user/$from_id.txt");
$co=$co_m_us+1;
file_put_contents("count/user/$from_id.txt","$co");
file_put_contents("count/user/all.txt","$co1");

if($msrd !=null){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"$msrd",
'reply_to_message_id'=>$message_id
]);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>" ",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"أشترك في القناة ليصلك التحديثات","url"=>"t.me/VEVoGamez"]]
]
])
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"تم ايقاف استقبال الرسائل من قبل مالك البوت ",
'reply_to_message_id'=>$message_id
]);
}
}else{

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"عذرا استقبال الصوتيات مغلق ❌",
'reply_to_message_id'=>$message_id
]);
}

}
//الصوتيات
if($audio and ! $forward ){
if($c_audio=="❌"or $c_photo== null){
if($estgbal=="on" or $estgbal==null){
bot('sendMessage',[
'chat_id'=>$yppee,
'text'=>"رساله من : [$name](tg://user?id=$from_id)
",
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
'reply_to_message_id'=>$message->reply_to_message->message_id-1,
]);
$get=bot('forwardMessage',[
'chat_id'=>$yppee,
'from_chat_id'=>$chat_id,
'message_id'=>$message_id

]);
$msg_id = $get->result->message_id-1;

$from_id_name="$chat_id=$name=$message_id";
file_put_contents("message/$msg_id.txt","$from_id_name");

$co_m_us=file_get_contents("count/user/$from_id.txt");
$co=$co_m_us+1;
file_put_contents("count/user/$from_id.txt","$co");
file_put_contents("count/user/all.txt","$co1");

if($msrd !=null){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"$msrd",
'reply_to_message_id'=>$message_id
]);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>" ",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"أشترك في القناة ليصلك التحديثات","url"=>"t.me/VEVoGamez"]]
]
])
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"تم ايقاف استقبال الرسائل من قبل مالك البوت ",
'reply_to_message_id'=>$message_id
]);
}
}else{

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"عذرا استقبال الموسيقى مغلق ❌",
'reply_to_message_id'=>$message_id
]);
}

}
//التوجية
if($forward ){
if($c_forward=="❌"or $c_forward== null){
if($estgbal=="on" or $estgbal==null){
bot('sendMessage',[
'chat_id'=>$yppee,
'text'=>"رساله من : [$name](tg://user?id=$from_id)
",
'parse_mode'=>"MarkDown",
'disable_web_page_preview'=>true,
'reply_to_message_id'=>$message->reply_to_message->message_id-1,
]);
$get=bot('forwardMessage',[
'chat_id'=>$yppee,
'from_chat_id'=>$chat_id,
'message_id'=>$message_id

]);
$msg_id = $get->result->message_id-1;

$from_id_name="$chat_id=$name=$message_id";
file_put_contents("message/$msg_id.txt","$from_id_name");

$co_m_us=file_get_contents("count/user/$from_id.txt");
$co=$co_m_us+1;
file_put_contents("count/user/$from_id.txt","$co");
file_put_contents("count/user/all.txt","$co1");

if($msrd !=null){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"$msrd",
'reply_to_message_id'=>$message_id
]);
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>" ",
'parse_mode'=>"markdown",
'reply_markup'=>json_encode([
     'inline_keyboard'=>[[['text'=>"أشترك في القناة ليصلك التحديثات","url"=>"t.me/VEVoGamez"]]
]
])
]);
}
}else{
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"تم ايقاف استقبال الرسائل من قبل مالك البوت ",
'reply_to_message_id'=>$message_id
]);
}
}else{

bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"عذرا استقبال التوجية مغلق ❌",
'reply_to_message_id'=>$message_id
]);
}

}
}


if(strstr($text,"t.me") == true or strstr($text,"telegram.me") == true or strstr($text,"https://") == true or strstr($text,"://") == true or strstr($text,"wWw.") == true or strstr($text,"WwW.") == true or strstr($text,"T.me/") == true or strstr($text,"WWW.") == true or strstr($caption,"t.me") == true or strstr($caption,"telegram.me")) {
if($users == "مقفول"){
	    bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ يمنع ارسال الروابط .",
        ]);
}
}
///////////////////////////////////

$botToken = "6859458694:AAGCOqJ-6lUwPW4dTL7bfVJ_pyBvA0mN_WI";

// التحقق من طلب البوت
$update = json_decode(file_get_contents("php://input"), true);

if (isset($update["message"])) {
    $chatId = $update["message"]["chat"]["id"];
    $messageText = $update["message"]["text"];
    handleUserMessage($chatId, $messageText);
} elseif (isset($update["callback_query"])) {
    $chatId = $update["callback_query"]["message"]["chat"]["id"];
    $callbackData = $update["callback_query"]["data"];
    handleCallbackQuery($chatId, $callbackData);
}

function handleUserMessage($chatId, $messageText) {
    if ($messageText == "/start") {
        $keyboard = [
            "inline_keyboard" => [
                [["text" => "ألعاب", "callback_data" => "games"], ["text" => "تطبيقات", "callback_data" => "apps"]],
                [["text" => "ألعاب بدون أنترنت", "callback_data" => "offline_games"], ["text" => "خدمات مدفوعة", "callback_data" => "paid_services"]]
            ]
        ];
        $replyMarkup = json_encode($keyboard);
        sendMessage($chatId, "أنت الآن في القائمة الرئيسية. يمكنك استخدام ما يلي:", $replyMarkup);
    }
}

function handleCallbackQuery($chatId, $callbackData) {
    if ($callbackData == "games") {
        $subKeyboard = [
            "inline_keyboard" => [
                [["text" => "العاب الحركة", "callback_data" => "action_games"], ["text" => "العاب المحاكاة", "callback_data" => "simulation_games"]],
                [["text" => "العاب إستراتيجية", "callback_data" => "strategy_games"], ["text" => "العاب الألغاز", "callback_data" => "puzzle_games"]],
                [["text" => "العاب كلاسيكية", "callback_data" => "classic_games"], ["text" => "العاب المغامرات", "callback_data" => "adventure_games"]],
                [["text" => "العاب تقمص الأدوار", "callback_data" => "rpg_games"], ["text" => "العاب الرياضة", "callback_data" => "sports_games"]],
                [["text" => "العاب خفيفة", "callback_data" => "casual_games"], ["text" => "العاب سباق", "callback_data" => "racing_games"]],
                [["text" => "العاب كلمات", "callback_data" => "word_games"], ["text" => "العاب الترفية", "callback_data" => "entertainment_games"]],
                [["text" => "العاب اللوحة", "callback_data" => "board_games"], ["text" => "العاب التعليمية", "callback_data" => "educational_games"]],
                [["text" => "العاب الورق والطاولة", "callback_data" => "card_games"], ["text" => "العاب الموسيقى والرقص", "callback_data" => "music_games"]],
                [["text" => "الرجوع", "callback_data" => "back"]]
            ]
        ];
        $replyMarkup = json_encode($subKeyboard);
        sendMessage($chatId, "التصنيفات المتاحة:", $replyMarkup);
    } elseif ($callbackData == "apps") {
        $subKeyboard = [
            "inline_keyboard" => [
                [["text" => "أدوات الفيديو", "callback_data" => "video_tools"], ["text" => "أجتماعي", "callback_data" => "social_apps"]],
                [["text" => "الأدوات", "callback_data" => "tools"], ["text" => "التعليم", "callback_data" => "education_apps"]],
                [["text" => "أعمال", "callback_data" => "business_apps"], ["text" => "الصور الفوتوغرافية", "callback_data" => "photography_apps"]],
                [["text" => "الجمال", "callback_data" => "beauty_apps"], ["text" => "الكتب والمراجع", "callback_data" => "books_apps"]],
                [["text" => "الموسيقى والصوت", "callback_data" => "music_audio_apps"], ["text" => "معلومات عامة", "callback_data" => "trivia_apps"]],
                [["text" => "نمط الحياة", "callback_data" => "lifestyle_apps"], ["text" => "Soon!", "callback_data" => "soon"]],
                [["text" => "الرجوع", "callback_data" => "back"]]
            ]
        ];
        $replyMarkup = json_encode($subKeyboard);
        sendMessage($chatId, "التصنيفات المتاحة:", $replyMarkup);
    } elseif ($callbackData == "back") {
        $keyboard = [
            "inline_keyboard" => [
                [["text" => "ألعاب", "callback_data" => "games"], ["text" => "تطبيقات", "callback_data" => "apps"]],
                [["text" => "ألعاب بدون أنترنت", "callback_data" => "offline_games"], ["text" => "خدمات مدفوعة", "callback_data" => "paid_services"]]
            ]
        ];
        $replyMarkup = json_encode($keyboard);
        sendMessage($chatId, "قائمة الأزرار:", $replyMarkup);
    } else {
        handleCategory($chatId, $callbackData);
    }
}

function handleCategory($chatId, $category) {
    $categories = [
        "action_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-الاكشن-والحركة.webp", "ألعاب الحركة : هو المكان المثالي لعشاق الإثارة والتشويق. انغمس في عوالم مليئة بالحركة السريعة والمعارك الشرسة، حيث يتطلب كل مستوى مهارات واستراتيجيات متقدمة للبقاء والتغلب على التحديات. سواء كنت تفضل ألعاب القتال الملحمية أو المطاردات المثيرة، ستجد في هذا التصنيف كل ما يرفع مستوى الأدرينالين لديك ويجعلك مستمتعاً لساعات طويلة. اكتشف الآن أفضل ألعاب الحركة واستعد للانغماس في مغامرات لا تنتهي!", "https://vevogame.com/action/"],
        "simulation_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-المحاكاة.webp", "ألعاب المحاكاة : هو بوابتك إلى عوالم واقعية مذهلة وتجارب حياتية مثيرة. عش حياة جديدة، سواء كانت إدارة مزرعة، قيادة طائرة، بناء مدينة، أو حتى تجربة مهن مختلفة. هذه الألعاب تقدم لك الفرصة لتجربة وتحقيق أحلامك بطرق تفاعلية وممتعة. مع تفاصيل دقيقة وتحكم كامل، ستشعر وكأنك تعيش في عالم حقيقي بكل تفاصيله. استعد لاستكشاف إمكانيات لا حصر لها وتحقيق كل ما تتخيله في عالم المحاكاة!", "https://vevogame.com/simulation/"],
        "strategy_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-إستراتيجية.webp", "ألعاب استراتيجية : هو المكان الذي يجتمع فيه التفكير العميق والتخطيط الذكي. اختبر قدراتك العقلية في إدارة الموارد، بناء الجيوش، ووضع الخطط العسكرية لتحقيق النصر. سواء كنت تحب الألعاب الحربية الكبرى أو الألعاب التي تتطلب تخطيطاً اقتصادياً دقيقاً، فإن هذا التصنيف يقدم لك تحديات مثيرة تحتاج إلى براعة وتفكير متقدم. انغمس في عوالم حيث القرارات الصحيحة هي مفتاح النجاح واستمتع بتجربة ألعاب تضع عقلك في حالة تأهب دائم!", "https://vevogame.com/strategy/"],
        "puzzle_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-الألغاز.webp", "ألعاب الألغاز : هو المكان المثالي لعشاق التحدي العقلي والذكاء. استمتع بحل الألغاز المعقدة واكتشاف الألغاز المثيرة التي تتطلب تفكيراً منطقياً وإبداعياً. من الألغاز الكلاسيكية إلى الألغاز الحديثة، ستجد هنا مجموعة متنوعة من التحديات التي تحفز العقل وتزيد من مهاراتك في التفكير والتحليل. انغمس في عوالم من الألغاز الغامضة واستعد لتجربة تسلية ذهنية لا مثيل لها!", "https://vevogame.com/puzzle/"],
        "classic_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-كلاسيكية.webp", "ألعاب كلاسيكية : هو بوابتك إلى عالم الألعاب التي لا تُنسى والتي شكلت جزءاً كبيراً من ذكرياتنا. استمتع بلعب الألعاب التي تعيدك إلى الأيام الخوالي، حيث البساطة والإبداع يجتمعان لتقديم تجارب لعب لا تنسى. من ألعاب الأركيد الشهيرة إلى الألغاز التقليدية، ستجد هنا كل ما يعيدك إلى جذور عالم الألعاب. استعد لرحلة في الزمن واستمتع بألعاب ذات طابع كلاسيكي يعيدك إلى أفضل اللحظات في تاريخ الألعاب!", "https://vevogame.com/arcade/"],
        "adventure_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-مغامرات.webp", "ألعاب المغامرات : يأخذك في رحلات ملحمية واستكشافات مذهلة. انغمس في عوالم خيالية وواقعية مليئة بالألغاز والتحديات المثيرة، حيث يمكنك أن تكون البطل الذي يخوض مغامرات لا تُنسى. سواء كنت تحب الاستكشاف في أعماق الغابات الغامضة، أو البحث عن الكنوز في الأماكن البعيدة، ستجد في هذا التصنيف تجارب رائعة تأخذك بعيدًا عن الواقع. استعد للانطلاق في مغامرات شيقة واكتشاف عوالم جديدة تنبض بالحياة!", "https://vevogame.com/adventure/"],
        "rpg_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-تقمص-الأدوار.webp", "ألعاب تقمص الأدوار : هو عالم لا حدود له من الخيال والإبداع. انغمس في شخصيات ملحمية وعش قصصاً مشوقة حيث تكون أنت البطل الذي يصنع قرارات مصيرية ويواجه تحديات كبيرة. سواء كنت تحب استكشاف العوالم السحرية، أو الانخراط في مغامرات الخيال العلمي، أو تجربة الحياة في عصور قديمة، فإن هذا التصنيف يقدم لك تجارب غامرة تأخذك بعيداً عن الواقع. استعد لرحلة ملحمية حيث تصبح فيها كل لحظة فرصة لتحقيق الأسطورة الخاصة بك!", "https://vevogame.com/role-playing/"],
        "sports_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-رياضة.webp", "ألعاب الرياضة : هو المكان المثالي لعشاق المنافسة والتحدي البدني. استعد لتجربة الحماس والإثارة في مجموعة متنوعة من الألعاب الرياضية، سواء كنت تفضل كرة القدم، كرة السلة، التنس، أو حتى سباقات السيارات. هذه الألعاب تضعك في قلب الأحداث الرياضية، حيث يمكنك تحسين مهاراتك، التنافس مع الأصدقاء، وتحقيق الانتصارات المذهلة. انغمس في أجواء الملاعب والصالات الرياضية واستمتع بتجارب رياضية واقعية تنقلك إلى قمة المنافسة!", "https://vevogame.com/sports/"],
        "casual_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-خفيفة.webp", "ألعاب خفيفة : هو المكان الذي يجمع بين المتعة وسهولة الوصول. اكتشف مجموعة متنوعة من الألعاب التي تقدم تجارب مسلية ومريحة، مثالية لقضاء وقت الفراغ أو الاسترخاء بعد يوم طويل. من الألغاز البسيطة إلى الألعاب البسيطة التي لا تتطلب مجهوداً كبيراً، تجد هنا خيارات ممتعة تناسب جميع الأعمار وتساعدك على الاستمتاع بلحظات من التسلية بدون تعقيد. استمتع بألعاب خفيفة تجعل كل لحظة ممتعة وسهلة!", "https://vevogame.com/casual/"],
        "racing_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-سباق.webp", "ألعاب سباق : هو المكان المثالي لعشاق السرعة والإثارة على الطرق. انغمس في تجارب سباق مليئة بالحركة والتشويق، حيث يمكنك قيادة سيارات، دراجات، أو حتى طائرات في منافسات مثيرة. اختر من بين مجموعة متنوعة من المسارات والتحديات، واستعرض مهاراتك في التحكم والتكتيك لتحقيق أسرع الأوقات وأعلى المراتب. استعد للانطلاق في مغامرات سباق لا تُنسى واختبر قدرتك على تحقيق النصر في السباقات المليئة بالتحديات!", "https://vevogame.com/racing/"],
        "word_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-كلمات.webp", "ألعاب كلمات : هو ملعبك لتحديات ذهنية ممتعة تحفز العقل وتختبر مهاراتك اللغوية. استمتع بحل الألغاز القائمة على الكلمات، والتلاعب بالحروف، واكتشاف الكلمات المخفية ضمن نصوص معقدة. من الكلمات المتقاطعة إلى الألعاب التي تتطلب تكوين كلمات جديدة، تجد هنا تجارب متنوعة تنشط ذاكرتك وتزيد من مهاراتك اللغوية. استعد للغوص في عالم مليء بالعبارات والألغاز الممتعة التي تجعل كل لحظة مليئة بالتفكير والترفيه!", "https://vevogame.com/word/"],
        "entertainment_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-الترفية.webp", "ألعاب الترفيه : هو وجهتك المثالية للهروب من الروتين اليومي والاستمتاع بوقت ممتع. اكتشف مجموعة واسعة من الألعاب التي تقدم تجارب مسلية وخفيفة، مما يجعلها مثالية لقضاء وقت ممتع مع الأصدقاء أو العائلة. سواء كنت تبحث عن ألعاب صغيرة، تحديات مرحة، أو تجارب ترفيهية مبتكرة، ستجد هنا كل ما يلزم لرفع معنوياتك وتجديد نشاطك. استمتع بألعاب ترفيهية تجعل كل لحظة مليئة بالضحك والفرح!", "https://vevogame.com/entertainment/"],
        "board_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-اللوحة.webp", "ألعاب اللوحة : هو عالم مليء بالمتعة والتفاعل الاجتماعي. انغمس في تجربة اللعب التقليدي مع مجموعة من الألعاب التي تُلعب على الطاولة، حيث تجمع بين الاستراتيجية، الحظ، والتعاون. من الألعاب الكلاسيكية مثل الشدة والدومينو إلى الألعاب الحديثة التي تتطلب التفكير والتخطيط، توفر لك هذه الفئة فرصة للتواصل مع الأصدقاء والعائلة والاستمتاع بأوقات ممتعة مليئة بالتحديات والإثارة. استعد لإعادة إحياء لحظات اللعب الجماعي مع ألعاب اللوحة التي تضيف البهجة إلى أي تجمع!", "https://vevogame.com/board/"],
        "educational_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-التعليمية.webp", "ألعاب التعليمية : هو عالم من المرح والتعلم في آن واحد. استمتع بألعاب مصممة لتعزيز المهارات الأكاديمية والفكرية بطرق مبتكرة وجذابة. من الألعاب التي تعزز المعرفة في الرياضيات والعلوم إلى الألعاب التي تحفز الإبداع وتطوير التفكير النقدي، ستجد هنا خيارات رائعة تساعدك على التعلم بطرق مسلية. مثالية للأطفال والكبار على حد سواء، هذه الألعاب تجعل التعلم تجربة ممتعة وتساهم في تعزيز مهاراتك بشكل ترفيهي وفعّال.", "https://vevogame.com/educational/"],
        "card_games" => ["https://vevogame.com/wp-content/uploads/2024/03/ألعاب-الورق-والطاولة.webp", "ألعاب الورق والطاولة : هو عالم يجمع بين الاستراتيجية والمتعة في جلسات اللعب التقليدي. من ألعاب الورق الكلاسيكية مثل البطاقات وألعاب الطاولة الشهيرة مثل الشدة والدومينو، إلى الألعاب الحديثة التي تُلعب على الطاولة، ستجد هنا تجارب ممتعة تتطلب المهارة والتخطيط. مثالية لجلسات اللعب مع الأصدقاء والعائلة، هذه الألعاب تقدم لك فرصة للتمتع بأوقات ممتعة وتحديات مثيرة. استعد للاستمتاع بمهاراتك واستراتيجياتك في ألعاب الورق والطاولة التي تجعل كل تجمع أكثر تفاعلاً ومتعة!", "https://vevogame.com/card/"],
        "music_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-الموسيقى-والرقص.webp", "ألعاب الموسيقى والرقص : هو عالم مليء بالإيقاع والحركة الذي يأخذك في رحلة ممتعة عبر عالم الموسيقى. انغمس في تجارب تفاعلية تجمع بين التحديات الموسيقية والرقصات الرائعة، حيث يمكنك تحسين مهاراتك في التوقيت والإيقاع من خلال اللعب. من الألعاب التي تتطلب منك تنسيق خطواتك مع الموسيقى إلى تحديات تفاعلية تتطلب دقة في الأداء، توفر لك هذه الألعاب فرصاً للاستمتاع والتعبير عن نفسك من خلال الحركات والألحان. استعد للرقص على إيقاع المرح والتمتع بتجربة موسيقية ترفع معنوياتك وتدفعك للحركة!", "https://vevogame.com/game-music/"],
        "paid_services" => ["https://vevogame.com/wp-content/uploads/2024/07/الخدمات-المدفوعة.webp", "🚀 مرحبًا بكم في عالم الإمكانيات اللامحدودة! 🚀

هل ترغب في الوصول إلى مزيد من المحتوى الحصري؟ هل تسعى لتجربة المزيد من المزايا الرائعة في لعبتك المفضلة دون أي قيود؟ إذا كانت إجابتك بنعم، فأنت في المكان المناسب!

مع خدمتنا المدفوعة، يمكنك الآن فتح باب الإمكانيات الغير محدودة لتطبيقاتك وألعابك المفضلة. لم تعد بحاجة للبحث عن طرق للوصول إلى المحتوى المدفوع، أو انتظار فترات طويلة للحصول على المميزات التي ترغب بها.

ما الذي تحصل عليه مع خدمتنا المدفوعة؟

** وصول فوري إلى جميع المزايا المدفوعة في التطبيقات والألعاب. **
** تجربة محتوى حصري غير متاح للمستخدمين العاديين. **
** دعم فني متخصص ومستمر لضمان تجربة استخدام سلسة. **

بدون إعلانات مزعجة، وبدون قيود، ستكون تجربتك مع لعبتك المفضلة أكثر متعة وسلاسة من أي وقت مضى.

انضم الينا الآن واحصل على فرصة لا تُضاهى لاكتشاف عالم جديد من المحتوى والمرح!

لا تفوت الفرصة، احصل على اشتراكك الآن وابدأ الاستمتاع بكل ما تحبه دون حدود!

🔥 للمزيد من المعلومات أرسل طلبك الان! 🔥

شكرًا لثقتكم بنا، ونتطلع إلى خدمتكم وتلبية احتياجاتكم المتزايدة.", "https://t.me/VEVoGamez"],
        "offline_games" => ["https://vevogame.com/wp-content/uploads/2024/03/العاب-اوف-لاين.webp", "ألعاب بدون إنترنت : هو خيارك المثالي لقضاء وقت ممتع أينما كنت، دون الحاجة إلى الاتصال بالشبكة. استمتع بمجموعة متنوعة من الألعاب التي لا تتطلب اتصالاً بالإنترنت، مما يجعلها مثالية للأوقات التي تكون فيها غير متصل أو في الأماكن التي تفتقر إلى الشبكة. من الألعاب التي تجمع بين التحديات والتسلية إلى ألعاب الألغاز والمغامرات التي تبقيك مشغولاً، ستجد هنا تجارب ممتعة ومليئة بالإثارة تجعل كل لحظة تستحق الاستمتاع. استعد للغوص في عالم من المرح والتسلية دون الحاجة للاتصال بالإنترنت!", "https://vevogame.com/offline/"],
        "video_tools" => ["https://modyoz.com/wp-content/uploads/2024/07/أدوات-الفيديو.webp", "أدوات الفيديو : هو وجهتك المثالية لكل ما تحتاجه لإنشاء، تعديل، ومشاركة محتوى الفيديو بجودة عالية. اكتشف مجموعة من التطبيقات التي توفر لك أدوات مبتكرة لتحرير الفيديو، إضافة تأثيرات خاصة، تحسين جودة الصوت والصورة، وإنشاء مقاطع مذهلة بكل سهولة. سواء كنت مبتدئاً أو محترفاً، ستجد هنا كل ما يلزم لتحويل أفكارك إلى واقع مرئي، ومشاركة إبداعاتك مع العالم. استعد لإطلاق العنان لإبداعك واستمتع بصناعة محتوى فيديو مذهل باستخدام أدوات الفيديو المتقدمة!", "https://vevogame.com/video-players/"],
        "social_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/أحدث-برامج-التواصل-الاجتماعي1.webp", "تطبيقات التواصل الاجتماعي : هو بوابتك للتواصل والتفاعل مع العالم. استمتع بمشاركة لحظاتك، متابعة أخبار أصدقائك، والانخراط في مجتمعات متنوعة عبر تطبيقات مصممة لتسهيل التواصل والتفاعل. سواء كنت تفضل النصوص، الصور، الفيديوهات، أو البث المباشر، ستجد هنا كل ما تحتاجه للبقاء على اتصال دائم وتوسيع دائرة معارفك.", "https://vevogame.com/social/"],
        "tools" => ["https://modyoz.com/wp-content/uploads/2024/07/تطبيقات-الأدوات.webp", "تطبيقات الأدوات : هو مجموعتك الشاملة من الأدوات الذكية التي تسهل حياتك اليومية. استمتع بتطبيقات توفر لك حلولاً متنوعة بدءاً من إدارة ملفاتك، قياس الأداء، تحرير النصوص، وحتى تحسين أداء جهازك. سواء كنت تحتاج إلى تطبيقات للأمان، الصيانة، أو تخصيص جهازك، ستجد هنا كل ما يلزم لجعل استخدامك للتكنولوجيا أكثر كفاءة وسهولة. اكتشف الآن مجموعة من الأدوات التي تجعل كل مهمة أبسط وأسرع!", "https://vevogame.com/tools/"],
        "education_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/تطبيقات-التعليم.jpg", "تطبيقات التعليم : هو عالم من المعرفة والتعلم في متناول يدك. اكتشف مجموعة متنوعة من التطبيقات التي تغطي مجالات عديدة مثل اللغات، العلوم، التكنولوجيا، والفنون. سواء كنت طالباً، معلماً، أو شخصاً يسعى لتعلم مهارات جديدة، ستجد هنا أدوات تعليمية تفاعلية تساعدك على الوصول إلى أهدافك التعليمية بسهولة وبمتعة. انغمس في تجربة تعليمية شاملة وابدأ رحلتك نحو اكتساب المعرفة اليوم!", "https://vevogame.com/education/"],
        "business_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/تطبيقات-أعمال.webp", "تطبيقات الأعمال : هو شريكك المثالي لتحقيق النجاح والتميز في عالم الأعمال. اكتشف مجموعة متنوعة من التطبيقات المصممة لتعزيز كفاءتك وإدارة مهامك بفعالية، سواء كنت تدير مشروعًا صغيرًا أو شركة كبيرة. من إدارة المشاريع والتواصل مع الفريق إلى تتبع النفقات وتحليل البيانات، ستجد هنا كل الأدوات التي تحتاجها لتبسيط عملياتك وتحقيق أهدافك بفعالية. ارتقِ بأعمالك إلى مستوى جديد من الإنتاجية والنجاح مع تطبيقات الأعمال المبتكرة!", "https://vevogame.com/business/"],
        "photography_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/الصور-الفوتوغرافية.webp", "تطبيقات الصور الفوتوغرافية : هو وجهتك لتحويل لحظاتك إلى ذكريات جميلة ومبدعة. اكتشف مجموعة من التطبيقات التي توفر لك أدوات متقدمة لتحرير الصور، إضافة تأثيرات مذهلة، تحسين الجودة، وإنشاء ألبومات رائعة. سواء كنت محترفاً في التصوير أو هاوياً، ستجد هنا كل ما تحتاجه لتحسين صورك وجعلها تنبض بالحياة. استعد لإطلاق العنان لإبداعك وتجربة متعة التصوير الفوتوغرافي بأعلى مستوياتها!", "https://vevogame.com/photography/"],
        "beauty_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/تطبيقات-الجمال.webp", "تطبيقات الجمال : هو وجهتك للحصول على نصائح وتوجيهات لتحسين جمالك ومظهرك الشخصي. استمتع بمجموعة من التطبيقات التي تقدم لك أدوات متقدمة للعناية بالبشرة، المكياج، وتسريحات الشعر، بالإضافة إلى النصائح الشخصية والتوجيهات لتحسين روتينك الجمالي. سواء كنت تبحث عن أحدث اتجاهات المكياج، تقنيات العناية بالبشرة، أو أفكار جديدة لتسريحات الشعر، ستجد هنا كل ما تحتاجه لتحقق أفضل إطلالة وتعزز جمالك الطبيعي.", "https://vevogame.com/beauty/"],
        "books_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/تطبيقات-الكتب-والمراجع.webp", "تطبيقات الكتب والمراجع : هو مكتبتك الرقمية التي تتيح لك الوصول إلى عالم من المعرفة والقراءة في متناول يدك. اكتشف مجموعة متنوعة من التطبيقات التي تقدم لك مكتبات إلكترونية شاملة، كتب جديدة، ومراجع متخصصة في مختلف المجالات. سواء كنت تبحث عن روايات مشوقة، كتب دراسية، أو مراجع أكاديمية، ستجد هنا كل ما يلزم لتوسيع آفاقك وزيادة معرفتك. استمتع بقراءة مريحة واستكشاف محتوى ثري أينما كنت ومع أي جهاز.", "https://vevogame.com/books-reference/"],
        "music_audio_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/تطبيقات-الموسيقى-والصوت.webp", "تطبيقات الموسيقى والصوت : هو وجهتك للاستمتاع بعالم غني من الألحان والصوتيات. اكتشف مجموعة من التطبيقات التي تقدم لك خدمات بث الموسيقى، تحرير الصوت، إنتاج الموسيقى، وتنظيم مكتبتك الصوتية. سواء كنت ترغب في الاستماع إلى أغانيك المفضلة، إنتاج مقاطع موسيقية أصلية، أو تعديل الصوت بجودة احترافية، ستجد هنا كل الأدوات التي تحتاجها لتحقيق تجربتك الصوتية المثلى. استعد لاكتشاف موسيقى جديدة وتعزيز إبداعك الصوتي بكل سهولة!", "https://vevogame.com/music-audio/"],
        "trivia_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/تطبيقات-معلومات-عامة.webp", "تطبيقات معلومات عامة : هو مصدرك المثالي للوصول إلى معلومات وحقائق متنوعة تغطي مجموعة واسعة من المواضيع. اكتشف تطبيقات تقدم لك تحديثات حول الأخبار، الحقائق الثقافية، معلومات تاريخية، واستفسارات علمية. سواء كنت تبحث عن تعزيز معرفتك العامة، متابعة الأحداث العالمية، أو استكشاف مواضيع جديدة، ستجد هنا كل ما تحتاجه لتوسيع آفاقك وتعزيز فهمك للعالم من حولك. استمتع بتجربة تعليمية شاملة مع تطبيقات توفر لك معلومات قيمة وبأسلوب سهل الوصول!", "https://vevogame.com/trivia/"],
        "lifestyle_apps" => ["https://modyoz.com/wp-content/uploads/2024/07/أسلوب-الحياة.webp", "تطبيقات نمط الحياة : هو دليلك لتحسين جوانب متعددة من حياتك اليومية بأسلوب عصري ومبتكر. اكتشف مجموعة من التطبيقات التي تساعدك في تنظيم حياتك الشخصية، تعزيز رفاهيتك، وإدارة أسلوب حياتك بفعالية. من تطبيقات تتبع العادات الصحية والتغذية، إلى أدوات لتنظيم الوقت وإدارة المهام، ستجد هنا كل ما يلزم لتحقيق توازن مثالي بين العمل والحياة الشخصية. استمتع بتجربة حياة أكثر تنظيمًا وراحة مع تطبيقات تساهم في تحسين جودة حياتك بشكل شامل!", "https://vevogame.com/lifestyle/"]
    ];

    if (isset($categories[$category])) {
        $photoUrl = $categories[$category][0];
        $caption = $categories[$category][1];
        $link = $categories[$category][2];
        sendPhotoWithLink($chatId, $photoUrl, $caption, $link);
    }
}

function sendMessage($chatId, $message, $replyMarkup = null) {
    global $botToken;
    $data = [
        "chat_id" => $chatId,
        "text" => $message
    ];
    if ($replyMarkup) {
        $data["reply_markup"] = $replyMarkup;
    }
    $url = "https://api.telegram.org/bot$botToken/sendMessage?" . http_build_query($data);
    file_get_contents($url);
}

function sendPhotoWithLink($chatId, $photoUrl, $caption, $link) {
    global $botToken;
    $keyboard = [
        "inline_keyboard" => [
            [["text" => "⚡️ التحميل الان  🚀", "url" => $link]]
        ]
    ];
    $replyMarkup = json_encode($keyboard);
    $data = [
        "chat_id" => $chatId,
        "photo" => $photoUrl,
        "caption" => $caption,
        "reply_markup" => $replyMarkup
    ];
    $url = "https://api.telegram.org/bot$botToken/sendPhoto?" . http_build_query($data);
    file_get_contents($url);
}


###### End of code ######

###################################

$update = json_decode(file_get_contents('php://input'), true);

if(isset($update["message"])) {
    $message = $update["message"];
    $chat_id = $message["chat"]["id"];
    $text = $message["text"];

    // التحقق مما إذا كانت الرسالة تحتوي على رابط من Google Play
    if(strpos($text, "https://play.google.com/store/apps/details?id=") !== false) {
        // رد مخصص لاستقبال الروابط وتجربتها
        $reply_message = "شكرًا لإرسال الرابط! نحن سعداء بتجربتة الان. سنقوم بالتحميل والإختبار قريبًا. ومع ذلك، لدينا مجموعة متنوعة من الألعاب والتطبيقات الأخرى. التي قد تثير اهتمامكم، يمكنكم استكشافها على متجر الخاص بنا. ⚡️« VEVoGame.com »⚡️ قد تجد طلبك الذي تبحث عنه!
شكرًا لتواصلكم معنا، ونتطلع لخدمتكم في المستقبل.";

        // إرسال الرد باستخدام الدالة sendTelegramMessage()
        sendTelegramMessage($chat_id, $reply_message);
    }
}

// دالة واحدة فقط لإرسال الرسائل
function sendTelegramMessage($chat_id, $message) {
    $apiToken = "6859458694:AAGCOqJ-6lUwPW4dTL7bfVJ_pyBvA0mN_WI";
    $url = "https://api.telegram.org/bot$apiToken/sendMessage?chat_id=$chat_id&text=" . urlencode($message);
    file_get_contents($url);
}

###################################

if($data == "hmaih"){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"
تحكم بحماية وقفل الوسائط المتعدده

❌ =  مفتوح
✅ = مقفل

لقفل الوسائط ارسل امر
قفل ( الصور - الفيديو - الملفات - التوجيه - الصوت - الموسيقى - الروابط - المعرفات )
مثال قفل التوجية
او
فتح التوجية

قفل الكل : لقفل جميع الوسائط
فتح الكل : لفتح جميع الوسائط
-----------------------",
'reply_to_message_id'=>$message->message_id,
'reply_markup'=>json_encode([
'inline_keyboard'=>[
[['text'=>" حالة الحماية  ",'callback_data'=>"halh"]],
]
])
]);
}
$c_photo=file_get_contents("data/photo.txt");
$c_video=file_get_contents("data/video.txt");
$c_document=file_get_contents("data/document.txt");
$c_sticker=file_get_contents("data/sticker.txt");
$c_voice=file_get_contents("data/voice.txt");
$c_audio=file_get_contents("data/audio.txt");
$c_forward =file_get_contents("data/audio.txt");

if($text=="قفل الصور" and in_array($from_id,$sudo)){
file_put_contents("data/photo.txt","✅");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم قفل الصور .",
        ]);
}

if($text=="فتح الصور" and in_array($from_id,$sudo)){
file_put_contents("data/photo.txt","❌");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم فتح الصور .",
        ]);
}
if($text=="قفل الفيديو" and in_array($from_id,$sudo)){
file_put_contents("data/video.txt","✅");

bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم قفل الفيديو .",
        ]);
}
if($text=="فتح الفيديو" and in_array($from_id,$sudo)){
file_put_contents("data/video.txt","❌");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم فتح الفيديو .",
        ]);
}

if($text=="قفل الملفات" and in_array($from_id,$sudo)){
file_put_contents("data/document.txt","✅");

bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم  $text .",
        ]);
}
if($text=="فتح الملفات" and in_array($from_id,$sudo)){
file_put_contents("data/document.txt","❌");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم $text .",
        ]);
}

if($text=="قفل الملصقات" and in_array($from_id,$sudo)){
file_put_contents("data/sticker.txt","✅");

bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم  $text .",
        ]);
}
if($text=="فتح الملصقات" and in_array($from_id,$sudo)){
file_put_contents("data/sticker.txt","❌");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم $text .",
        ]);
}

if($text=="قفل الصوتيات" and in_array($from_id,$sudo)){
file_put_contents("data/voice.txt","✅");

bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم  $text .",
        ]);
}
if($text=="فتح الصوتيات" and in_array($from_id,$sudo)){
file_put_contents("data/voice.txt","❌");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم $text .",
        ]);
}


if($text=="قفل الموسيقى" and in_array($from_id,$sudo)){
file_put_contents("data/audio.txt","✅");

bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم  $text .",
        ]);
}
if($text=="فتح الموسيقى" and in_array($from_id,$sudo)){
file_put_contents("data/audio.txt","❌");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم $text .",
        ]);
}
if($text=="قفل التوجية" and in_array($from_id,$sudo)){
file_put_contents("data/forward.txt","✅");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم  $text .",
        ]);
}
if($text=="فتح التوجية" and in_array($from_id,$sudo)){
file_put_contents("data/forward.txt","❌");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم $text .",
        ]);
}

if($text=="قفل الكل" and in_array($from_id,$sudo)){
file_put_contents("data/forward.txt","✅");
file_put_contents("data/audio.txt","✅");
file_put_contents("data/voice.txt","✅");
file_put_contents("data/sticker.txt","✅");
file_put_contents("data/document.txt","✅");
file_put_contents("data/video.txt","✅");
file_put_contents("data/photo.txt","✅");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم  $text .",
        ]);
}
if($text=="فتح الكل" and in_array($from_id,$sudo)){
file_put_contents("data/forward.txt","❌");
file_put_contents("data/audio.txt","❌");
file_put_contents("data/voice.txt","❌");
file_put_contents("data/sticker.txt","❌");
file_put_contents("data/document.txt","❌");
file_put_contents("data/video.txt","❌");
file_put_contents("data/photo.txt","❌");
bot('sendmessage',[
       'chat_id'=>$chat_id,
        'text'=>"◾ تم  $text .",
        ]);
}



/////// أوامر البوت ///////


$api_url = "https://api.telegram.org/bot$token/";

$update = json_decode(file_get_contents("php://input"), TRUE);
$message = $update['message'];
$chat_id = $message['chat']['id'];
$text = $message['text'];

if ($text == '/start') {
    sendMessage($chat_id, 'هذا هو البوت 🤖 الخاص بفريق VEVoGamez , كيف يمكنني مساعدتك؟');
}
if ($text == '/help') {
    sendMessage($chat_id, 'أنتَ الآن في قسم المساعدة! يُرجى كتابة رسالتك وسنقوم بالرد عليها في أسرع وقت ممكن. شكرًا لثقتك بنا!');
}


if ($text == '/info') {
    sendMessage($chat_id, 'مرحبًا بك في قسم المعلومات! نحن هنا لتقديم كل ما تحتاج إلى معرفته حول خدماتنا. نحن ملتزمون بتقديم أحدث المعلومات والتحديثات لتجربة مستخدم متميزة.

موقعنا يوفر محتوى متنوع من التطبيقات والألعاب الشيقة، بالإضافة إلى خدمة مميزة للمشتركين لتحميل تطبيقاتهم المفضلة بسهولة وبلا حدود.

شكرًا لاهتمامك بخدماتنا، ونتطلع إلى خدمتك وتلبية احتياجاتك بأفضل شكل ممكن!');
}




/////// أوامر البوت ///////


#################################################


// هنا نفترض أنك قد قمت بتخزين روابط التطبيقات أو الألعاب في مصفوفة مثلاً
$appLinks = [
'ملكة الموضة' => 'https://vevogame.com/fashion-queen/',
'ملكه الموضه' => 'https://vevogame.com/fashion-queen/',
'ملكة الموضة مهكرة' => 'https://vevogame.com/fashion-queen/',
'ملكه الموضه مهكره' => 'https://vevogame.com/fashion-queen/',
'أسرار البنات' => 'https://vevogame.com/my-story-tamatem/',
'اسرار البنات' => 'https://vevogame.com/my-story-tamatem/',
'اسرار بنات' => 'https://vevogame.com/my-story-tamatem/',
'لعبة اسرار البنات مهكرة' => 'https://vevogame.com/my-story-tamatem/',
'كلاش اوف كلانس' => 'https://vevogame.com/clash-of-clans/',
'كلاش مهكره' => 'https://vevogame.com/clash-of-clans/',
'كلاش أوف كلانس' => 'https://vevogame.com/clash-of-clans/',
'لعبة كلاش اوف كلانس مهكرة' => 'https://vevogame.com/clash-of-clans/',
'Clash of Clans' => 'https://vevogame.com/clash-of-clans/',
'هاي داي' => 'https://vevogame.com/hay-day/',
'Gangstar Vegas' => 'https://vevogame.com/gangstar-vegas/',
'Hay Day' => 'https://vevogame.com/hay-day/',
'هاي داي مهكرة' => 'https://vevogame.com/hay-day/',
'هاي داي مهكره' => 'https://vevogame.com/hay-day/',
'كلاش رويال' => 'https://vevogame.com/clash-royale/',

    // يمكنك إضافة مزيد من التطبيقات أو الألعاب حسب الحاجة
];

// هنا قد يكون رد البوت على المستخدم
if (isset($appLinks[$text])) {
    $response = "لقد طلبت 👈🏻 $text! يمكنك تحميله من هنا ⬇️: " . $appLinks[$text];
    bot('sendMessage', [
        'chat_id' => $chat_id,
        'text' => $response,
    ]);
}


#################################################


$c_photo=file_get_contents("data/photo.txt");
$c_video=file_get_contents("data/video.txt");
$c_document=file_get_contents("data/document.txt");
$c_sticker=file_get_contents("data/sticker.txt");
$c_voice=file_get_contents("data/voice.txt");
$c_audio=file_get_contents("data/audio.txt");
$c_forward =file_get_contents("data/audio.txt");

if($data == "halh"){
bot('sendMessage',[
'chat_id'=>$chat_id,
'text'=>"
تحكم بحماية وقفل الوسائط المتعدده

❌ =  مفتوح
✅ = مقفل

⭕ الحالة /
▫الصور : $c_photo
▫الفيديو : $c_video
▫الملفات : $c_document
▫الملصقات : $c_sticker
▫ الصوتيات : $c_voice
▫الموسيقى : $c_audio
▫التوجية : $c_forward
▫الراوبط :

-----------------------",
'reply_to_message_id'=>$message->message_id,
]);
}