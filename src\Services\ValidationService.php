<?php

namespace TelegramBot\Services;

/**
 * Validation Service Class
 * 
 * Handles input validation, sanitization, and security checks.
 */
class ValidationService
{
    /**
     * Validate user ID
     */
    public function validateUserId($userId)
    {
        return is_numeric($userId) && $userId > 0 && strlen($userId) <= 15;
    }

    /**
     * Validate message text
     */
    public function validateMessageText($text)
    {
        if (empty($text) || !is_string($text)) {
            return false;
        }

        // Check length
        if (strlen($text) > MAX_MESSAGE_LENGTH) {
            return false;
        }

        return true;
    }

    /**
     * Sanitize text input
     */
    public function sanitizeText($text)
    {
        if (!is_string($text)) {
            return '';
        }

        // Remove null bytes
        $text = str_replace("\0", '', $text);
        
        // Trim whitespace
        $text = trim($text);
        
        // Remove excessive whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        return $text;
    }

    /**
     * Validate channel username
     */
    public function validateChannelUsername($username)
    {
        if (empty($username)) {
            return false;
        }

        // Remove @ if present
        $username = ltrim($username, '@');
        
        // Check format
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]{4,31}$/', $username)) {
            return false;
        }

        return true;
    }

    /**
     * Check for spam patterns
     */
    public function isSpam($text)
    {
        if (empty($text)) {
            return false;
        }

        $spamPatterns = [
            '/(.)\1{10,}/', // Repeated characters
            '/[A-Z]{20,}/', // Too many capitals
            '/(.{1,10})\1{5,}/', // Repeated patterns
        ];

        foreach ($spamPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for prohibited links
     */
    public function hasProhibitedLinks($text)
    {
        if (empty($text)) {
            return false;
        }

        $linkPatterns = [
            '/t\.me\//',
            '/telegram\.me\//',
            '/https?:\/\//',
            '/www\./i',
            '/bit\.ly\//',
            '/tinyurl\.com\//',
        ];

        foreach ($linkPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate promotion code
     */
    public function validatePromotionCode($code)
    {
        return is_numeric($code) && strlen($code) === 6;
    }

    /**
     * Validate callback data
     */
    public function validateCallbackData($data)
    {
        if (empty($data) || !is_string($data)) {
            return false;
        }

        // Check length (Telegram limit is 64 bytes)
        if (strlen($data) > 64) {
            return false;
        }

        // Check for valid characters
        if (!preg_match('/^[a-zA-Z0-9_\-\s]+$/', $data)) {
            return false;
        }

        return true;
    }

    /**
     * Sanitize filename
     */
    public function sanitizeFilename($filename)
    {
        // Remove directory traversal attempts
        $filename = basename($filename);
        
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Limit length
        if (strlen($filename) > 255) {
            $filename = substr($filename, 0, 255);
        }

        return $filename;
    }

    /**
     * Validate JSON data
     */
    public function validateJson($json)
    {
        if (empty($json)) {
            return false;
        }

        json_decode($json);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Check message rate limiting
     */
    public function checkMessageRate($userId, $timeWindow = 60, $maxMessages = 20)
    {
        $rateLimitFile = STORAGE_PATH . "rate_limit/$userId.json";
        
        if (!file_exists($rateLimitFile)) {
            return true;
        }

        $data = json_decode(file_get_contents($rateLimitFile), true);
        if (!$data) {
            return true;
        }

        $currentTime = time();
        $messages = array_filter($data, function($timestamp) use ($currentTime, $timeWindow) {
            return ($currentTime - $timestamp) <= $timeWindow;
        });

        return count($messages) < $maxMessages;
    }

    /**
     * Log message for rate limiting
     */
    public function logMessageForRateLimit($userId)
    {
        $rateLimitDir = STORAGE_PATH . 'rate_limit/';
        if (!is_dir($rateLimitDir)) {
            mkdir($rateLimitDir, 0755, true);
        }

        $rateLimitFile = $rateLimitDir . "$userId.json";
        
        $data = [];
        if (file_exists($rateLimitFile)) {
            $data = json_decode(file_get_contents($rateLimitFile), true) ?: [];
        }

        $data[] = time();
        
        // Keep only last 50 entries
        if (count($data) > 50) {
            $data = array_slice($data, -50);
        }

        file_put_contents($rateLimitFile, json_encode($data));
    }

    /**
     * Validate broadcast message
     */
    public function validateBroadcastMessage($message)
    {
        if (empty($message)) {
            return ['valid' => false, 'error' => 'الرسالة فارغة'];
        }

        if (strlen($message) > MAX_MESSAGE_LENGTH) {
            return ['valid' => false, 'error' => 'الرسالة طويلة جداً'];
        }

        if ($this->isSpam($message)) {
            return ['valid' => false, 'error' => 'الرسالة تحتوي على محتوى مشبوه'];
        }

        return ['valid' => true];
    }

    /**
     * Clean and validate admin input
     */
    public function validateAdminInput($input, $type)
    {
        $input = $this->sanitizeText($input);

        switch ($type) {
            case 'user_id':
                return $this->validateUserId($input) ? $input : false;
                
            case 'channel':
                return $this->validateChannelUsername($input) ? $input : false;
                
            case 'promotion_code':
                return $this->validatePromotionCode($input) ? $input : false;
                
            case 'message':
                return $this->validateMessageText($input) ? $input : false;
                
            default:
                return false;
        }
    }

    /**
     * Check for SQL injection patterns (even though we use files)
     */
    public function hasSqlInjection($input)
    {
        $patterns = [
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i',
            '/(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/i',
            '/[\'";]/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate file upload
     */
    public function validateFileUpload($fileInfo)
    {
        if (!isset($fileInfo['file_id'])) {
            return false;
        }

        // Check file size if available
        if (isset($fileInfo['file_size']) && $fileInfo['file_size'] > 50 * 1024 * 1024) { // 50MB limit
            return false;
        }

        return true;
    }

    /**
     * Check for XSS patterns
     */
    public function hasXss($input)
    {
        $patterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi',
            '/javascript:/i',
            '/on\w+\s*=/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Comprehensive input validation
     */
    public function validateInput($input, $context = 'general')
    {
        if (!is_string($input)) {
            return ['valid' => false, 'error' => 'نوع البيانات غير صحيح'];
        }

        // Basic sanitization
        $input = $this->sanitizeText($input);

        // Check for empty input
        if (empty($input)) {
            return ['valid' => false, 'error' => 'البيانات فارغة'];
        }

        // Check for malicious patterns
        if ($this->hasSqlInjection($input)) {
            return ['valid' => false, 'error' => 'محتوى مشبوه تم اكتشافه'];
        }

        if ($this->hasXss($input)) {
            return ['valid' => false, 'error' => 'محتوى مشبوه تم اكتشافه'];
        }

        // Context-specific validation
        switch ($context) {
            case 'message':
                if (!$this->validateMessageText($input)) {
                    return ['valid' => false, 'error' => 'نص الرسالة غير صحيح'];
                }
                break;
                
            case 'user_id':
                if (!$this->validateUserId($input)) {
                    return ['valid' => false, 'error' => 'معرف المستخدم غير صحيح'];
                }
                break;
                
            case 'channel':
                if (!$this->validateChannelUsername($input)) {
                    return ['valid' => false, 'error' => 'معرف القناة غير صحيح'];
                }
                break;
        }

        return ['valid' => true, 'data' => $input];
    }
}
