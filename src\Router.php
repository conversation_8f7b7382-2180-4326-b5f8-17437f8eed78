<?php

namespace TelegramBot;

use TelegramBot\Controllers\AdminController;
use TelegramBot\Controllers\UserController;
use TelegramBot\Controllers\BroadcastController;
use TelegramBot\Controllers\GameController;
use TelegramBot\Services\FileService;
use TelegramBot\Services\UserService;
use TelegramBot\Services\MessageService;
use TelegramBot\Services\ValidationService;

/**
 * Router Class
 * 
 * Routes incoming messages and callback queries to appropriate controllers.
 */
class Router
{
    private $api;
    private $fileService;
    private $userService;
    private $messageService;
    private $validationService;
    private $adminController;
    private $userController;
    private $broadcastController;
    private $gameController;

    public function __construct(TelegramAPI $api, $config)
    {
        $this->api = $api;
        $this->fileService = new FileService($config);
        $this->userService = new UserService($this->fileService, $api);
        $this->messageService = new MessageService($this->fileService, $api, $this->userService);
        $this->validationService = new ValidationService();

        // Initialize controllers
        $this->adminController = new AdminController($api, $this->fileService, $this->userService, $this->validationService);
        $this->userController = new UserController($api, $this->fileService, $this->userService, $this->messageService, $this->validationService);
        $this->broadcastController = new BroadcastController($api, $this->fileService, $this->userService, $this->validationService);
        $this->gameController = new GameController($api, $this->fileService);
    }

    /**
     * Route incoming update
     */
    public function route($update)
    {
        try {
            // Prevent duplicate processing
            if (!$this->isDuplicateUpdate($update)) {
                if (isset($update['message'])) {
                    return $this->handleMessage($update['message']);
                } elseif (isset($update['callback_query'])) {
                    return $this->handleCallbackQuery($update['callback_query']);
                }
            }
        } catch (Exception $e) {
            $this->fileService->logMessage('error', 'Router error: ' . $e->getMessage(), [
                'update' => $update,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }

        return false;
    }

    /**
     * Check if update is duplicate
     */
    private function isDuplicateUpdate($update)
    {
        $updateId = $update['update_id'] ?? null;
        if (!$updateId) {
            return false;
        }

        $duplicateFile = STORAGE_PATH . 'temp/last_update_id.txt';

        // Create temp directory if it doesn't exist
        if (!is_dir(dirname($duplicateFile))) {
            mkdir(dirname($duplicateFile), 0755, true);
        }

        $lastUpdateId = 0;
        if (file_exists($duplicateFile)) {
            $lastUpdateId = (int)file_get_contents($duplicateFile);
        }

        // Check if this update was already processed
        if ($updateId <= $lastUpdateId) {
            $this->fileService->logMessage('warning', "Duplicate update detected: $updateId (last: $lastUpdateId)");
            return true;
        }

        // Store current update ID
        file_put_contents($duplicateFile, $updateId);
        return false;
    }

    /**
     * Handle incoming message
     */
    private function handleMessage($message)
    {
        $userId = $message['from']['id'];
        $chatId = $message['chat']['id'];
        $text = $message['text'] ?? '';

        // Log message
        if (ENABLE_MESSAGE_LOGGING) {
            $this->fileService->logMessage('info', "Message received from user $userId", [
                'user_id' => $userId,
                'chat_id' => $chatId,
                'text' => substr($text, 0, 100) // Log first 100 chars only
            ]);
        }

        // Check if user is in broadcast mode (admin only)
        if ($this->userService->isAdmin($userId)) {
            $broadcastState = $this->broadcastController->getBroadcastState();
            if (!empty($broadcastState['type']) && $broadcastState['user'] == $userId) {
                return $this->broadcastController->processBroadcast($message, $broadcastState['type']);
            }

            // Check if admin is replying to a forwarded message
            if (isset($message['reply_to_message'])) {
                $replyResult = $this->handleAdminReply($message);
                if ($replyResult !== false) {
                    return $replyResult;
                }
            }

            // Check for admin state-based commands
            if ($this->handleAdminStateCommands($message)) {
                return true;
            }
        }

        // Handle commands
        if (isset($message['text'])) {
            switch ($text) {
                case '/start':
                    return $this->userController->handleStart($message);

                default:
                    // Check for game/app text requests
                    if ($this->gameController->handleTextRequest($chatId, $text)) {
                        return true;
                    }

                    // Handle regular user message
                    return $this->userController->handleUserMessage($message);
            }
        }

        // Handle media messages
        return $this->userController->handleUserMessage($message);
    }

    /**
     * Handle callback query
     */
    private function handleCallbackQuery($callbackQuery)
    {
        $userId = $callbackQuery['from']['id'];
        $chatId = $callbackQuery['message']['chat']['id'];
        $messageId = $callbackQuery['message']['message_id'];
        $data = $callbackQuery['data'];

        // Answer callback query
        $this->api->answerCallbackQuery($callbackQuery['id']);

        // Validate callback data
        if (!$this->validationService->validateCallbackData($data)) {
            return false;
        }

        // Log callback
        if (ENABLE_MESSAGE_LOGGING) {
            $this->fileService->logMessage('info', "Callback received from user $userId", [
                'user_id' => $userId,
                'data' => $data
            ]);
        }

        // Route based on callback data
        return $this->routeCallback($userId, $chatId, $messageId, $data);
    }

    /**
     * Route callback to appropriate handler
     */
    private function routeCallback($userId, $chatId, $messageId, $data)
    {
        // Admin panel routes
        if (strpos($data, 'admin_') === 0) {
            if (!$this->userService->isAdmin($userId)) {
                return $this->api->editMessageText($chatId, $messageId, ERROR_PERMISSION_DENIED);
            }
            return $this->handleAdminCallback($chatId, $messageId, $data);
        }

        // Broadcast routes
        if (strpos($data, 'broadcast_') === 0) {
            if (!$this->userService->isAdmin($userId)) {
                return $this->api->editMessageText($chatId, $messageId, ERROR_PERMISSION_DENIED);
            }
            return $this->handleBroadcastCallback($chatId, $messageId, $data);
        }

        // Game/App routes
        if (strpos($data, 'games_') === 0 || strpos($data, 'apps_') === 0 || 
            strpos($data, 'game_') === 0 || strpos($data, 'app_') === 0 ||
            in_array($data, ['main_menu', 'offline_games', 'paid_services'])) {
            return $this->handleGameCallback($chatId, $messageId, $data);
        }

        // Message management routes (admin only)
        if (strpos($data, 'edit_msg_') === 0 || strpos($data, 'del_msg_') === 0) {
            if (!$this->userService->isAdmin($userId)) {
                return false;
            }
            return $this->handleMessageCallback($chatId, $messageId, $data);
        }

        return false;
    }

    /**
     * Handle admin callbacks
     */
    private function handleAdminCallback($chatId, $messageId, $data)
    {
        switch ($data) {
            case 'admin_panel':
                return $this->adminController->showAdminPanel($chatId, $messageId);

            case 'admin_management':
                return $this->adminController->handleAdminManagement($chatId, $messageId);
            case 'admin_management_add':
                return $this->adminController->handleAdminManagement($chatId, $messageId, 'add');
            case 'admin_management_remove':
                return $this->adminController->handleAdminManagement($chatId, $messageId, 'remove');
            case 'admin_management_list':
                return $this->adminController->handleAdminManagement($chatId, $messageId, 'list');

            case 'admin_subscription':
                return $this->adminController->handleSubscriptionManagement($chatId, $messageId);
            case 'admin_subscription_add_channel':
                return $this->adminController->handleSubscriptionManagement($chatId, $messageId, 'add_channel');
            case 'admin_subscription_remove_channel':
                return $this->adminController->handleSubscriptionManagement($chatId, $messageId, 'remove_channel');
            case 'admin_subscription_list_channels':
                return $this->adminController->handleSubscriptionManagement($chatId, $messageId, 'list_channels');

            case 'admin_statistics':
                return $this->adminController->showStatistics($chatId, $messageId);

            case 'admin_bot_status':
                return $this->adminController->toggleBotStatus($chatId, $messageId);

            case 'admin_notifications':
                return $this->adminController->toggleNotifications($chatId, $messageId);

            case 'admin_media_protection':
                return $this->adminController->handleMediaProtection($chatId, $messageId);

            case 'admin_messages':
                return $this->adminController->handleMessageSettings($chatId, $messageId);

            case 'admin_edit_welcome':
                return $this->handleEditWelcomeMessage($chatId, $messageId);
            case 'admin_edit_auto_reply':
                return $this->handleEditAutoReply($chatId, $messageId);
            case 'admin_toggle_auto_reply':
                return $this->adminController->toggleAutoReply($chatId, $messageId);

            default:
                // Handle media protection toggles
                if (strpos($data, 'admin_media_') === 0) {
                    $mediaType = str_replace('admin_media_', '', $data);
                    return $this->adminController->handleMediaProtection($chatId, $messageId, $mediaType);
                }

                // Handle channel removal
                if (strpos($data, 'admin_remove_channel_') === 0) {
                    $channelIndex = str_replace('admin_remove_channel_', '', $data);
                    return $this->adminController->removeChannel($chatId, $messageId, $channelIndex);
                }
                break;
        }

        return false;
    }

    /**
     * Handle broadcast callbacks
     */
    private function handleBroadcastCallback($chatId, $messageId, $data)
    {
        switch ($data) {
            case 'admin_broadcast':
            case 'broadcast_panel':
                return $this->broadcastController->showBroadcastPanel($chatId, $messageId);

            case 'broadcast_forward':
                return $this->broadcastController->startBroadcast($chatId, $messageId, 'forward');
            case 'broadcast_html':
                return $this->broadcastController->startBroadcast($chatId, $messageId, 'html');
            case 'broadcast_markdown':
                return $this->broadcastController->startBroadcast($chatId, $messageId, 'markdown');
            case 'broadcast_text':
                return $this->broadcastController->startBroadcast($chatId, $messageId, 'text');

            case 'broadcast_cancel':
                return $this->broadcastController->cancelBroadcast($chatId, $messageId);

            default:
                // Handle broadcast deletion
                if (strpos($data, 'broadcast_delete_') === 0) {
                    $broadcastId = str_replace('broadcast_delete_', '', $data);
                    return $this->broadcastController->deleteBroadcast($chatId, $messageId, $broadcastId);
                }
                break;
        }

        return false;
    }

    /**
     * Handle game/app callbacks
     */
    private function handleGameCallback($chatId, $messageId, $data)
    {
        switch ($data) {
            case 'main_menu':
                return $this->gameController->showMainMenu($chatId, $messageId);
            case 'games_menu':
                return $this->gameController->showGamesMenu($chatId, $messageId);
            case 'apps_menu':
                return $this->gameController->showAppsMenu($chatId, $messageId);
            case 'offline_games':
                return $this->gameController->showOfflineGames($chatId, $messageId);
            case 'paid_services':
                return $this->gameController->showPaidServices($chatId, $messageId);

            default:
                // Handle game categories
                if (strpos($data, 'games_') === 0) {
                    $category = str_replace('games_', '', $data);
                    return $this->gameController->showGameCategory($chatId, $messageId, $category);
                }

                // Handle app categories
                if (strpos($data, 'apps_') === 0) {
                    $category = str_replace('apps_', '', $data);
                    return $this->gameController->showAppCategory($chatId, $messageId, $category);
                }

                // Handle specific game
                if (strpos($data, 'game_') === 0) {
                    $gameId = str_replace('game_', '', $data);
                    return $this->gameController->showGameDetails($chatId, $messageId, $gameId);
                }

                // Handle specific app
                if (strpos($data, 'app_') === 0) {
                    $appId = str_replace('app_', '', $data);
                    return $this->gameController->showAppDetails($chatId, $messageId, $appId);
                }
                break;
        }

        return false;
    }

    /**
     * Handle message management callbacks
     */
    private function handleMessageCallback($chatId, $messageId, $data)
    {
        if (strpos($data, 'edit_msg_') === 0) {
            $targetMessageId = str_replace('edit_msg_', '', $data);
            // Set edit state and ask for new message
            $this->fileService->writeFile(ADMIN_PATH . 'edit_state.txt', $targetMessageId);
            $this->fileService->writeFile(ADMIN_PATH . 'edit_user.txt', $chatId);

            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '• إلغاء •', 'callback_data' => 'cancel_edit']]
            ]);

            return $this->api->editMessageText($chatId, $messageId, 
                "أرسل النص الجديد لتعديل الرسالة:", [
                'reply_markup' => $keyboard
            ]);
        }

        if (strpos($data, 'del_msg_') === 0) {
            $targetMessageId = str_replace('del_msg_', '', $data);
            $result = $this->messageService->deleteMessage($targetMessageId);
            
            return $this->api->editMessageText($chatId, $messageId, $result['message'], [
                'parse_mode' => 'Markdown'
            ]);
        }

        return false;
    }

    /**
     * Handle admin state-based commands
     */
    private function handleAdminStateCommands($message)
    {
        $userId = $message['from']['id'];
        $chatId = $message['chat']['id'];
        $text = $message['text'] ?? '';

        // Handle channel addition
        $state = $this->fileService->readFile(ADMIN_PATH . 'state.txt');
        if (trim($state) === 'adding_channel' && !empty($text)) {
            $validation = $this->validationService->validateInput($text, 'channel');
            if ($validation['valid']) {
                $this->fileService->addChannel($validation['data']);
                $this->fileService->deleteFile(ADMIN_PATH . 'state.txt');
                
                $this->api->sendMessage($chatId, "تم حفظ القناة بنجاح ✅");
                return true;
            } else {
                $this->api->sendMessage($chatId, "❌ " . $validation['error']);
                return true;
            }
        }

        // Handle message editing
        $editState = $this->fileService->readFile(ADMIN_PATH . 'edit_state.txt');
        $editUser = $this->fileService->readFile(ADMIN_PATH . 'edit_user.txt');

        if (!empty($editState) && trim($editUser) == $chatId && !empty($text)) {
            $result = $this->messageService->editMessage($editState, $text, $chatId);

            $this->fileService->deleteFile(ADMIN_PATH . 'edit_state.txt');
            $this->fileService->deleteFile(ADMIN_PATH . 'edit_user.txt');

            $this->api->sendMessage($chatId, $result['message'], [
                'parse_mode' => 'Markdown'
            ]);
            return true;
        }

        // Handle welcome message editing
        $welcomeEditState = $this->fileService->readFile(ADMIN_PATH . 'edit_welcome_state.txt');
        if (!empty($welcomeEditState) && trim($welcomeEditState) == $chatId && !empty($text)) {
            $this->fileService->setWelcomeMessage($text);
            $this->fileService->deleteFile(ADMIN_PATH . 'edit_welcome_state.txt');

            $this->api->sendMessage($chatId, "تم تحديث رسالة الترحيب بنجاح ✅");
            return true;
        }

        // Handle auto reply editing
        $autoReplyEditState = $this->fileService->readFile(ADMIN_PATH . 'edit_auto_reply_state.txt');
        if (!empty($autoReplyEditState) && trim($autoReplyEditState) == $chatId && !empty($text)) {
            $this->fileService->setAutoReplyMessage($text);
            $this->fileService->deleteFile(ADMIN_PATH . 'edit_auto_reply_state.txt');

            $this->api->sendMessage($chatId, "تم تحديث رسالة الرد التلقائي بنجاح ✅");
            return true;
        }

        return false;
    }

    /**
     * Handle admin reply to forwarded message
     */
    private function handleAdminReply($message)
    {
        $replyToMessageId = $message['reply_to_message']['message_id'];
        $adminChatId = $message['chat']['id'];

        // Log the reply attempt
        $this->fileService->logMessage('info', "Admin reply attempt", [
            'admin_chat_id' => $adminChatId,
            'reply_to_message_id' => $replyToMessageId,
            'admin_message' => substr($message['text'] ?? '', 0, 100)
        ]);

        // Try to send reply to user
        $result = $this->messageService->sendReplyToUser($message, $replyToMessageId);

        if ($result['success']) {
            // Send confirmation to admin
            $keyboard = TelegramAPI::createInlineKeyboard([
                [
                    ['text' => '✏️ تعديل', 'callback_data' => 'edit_msg_' . $result['reply_message_id']],
                    ['text' => '🗑️ حذف', 'callback_data' => 'del_msg_' . $result['reply_message_id']]
                ]
            ]);

            $this->api->sendMessage($adminChatId, $result['message'], [
                'parse_mode' => 'Markdown',
                'reply_markup' => $keyboard,
                'reply_to_message_id' => $message['message_id']
            ]);

            return true;
        } else {
            // Send error message to admin
            $this->api->sendMessage($adminChatId, "❌ " . $result['message'], [
                'reply_to_message_id' => $message['message_id']
            ]);

            return true;
        }
    }

    /**
     * Handle edit welcome message
     */
    private function handleEditWelcomeMessage($chatId, $messageId)
    {
        // Set state for welcome message editing
        $this->fileService->writeFile(ADMIN_PATH . 'edit_welcome_state.txt', $chatId);

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• إلغاء •', 'callback_data' => 'admin_messages']]
        ]);

        return $this->api->editMessageText($chatId, $messageId,
            "أرسل رسالة الترحيب الجديدة:", [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Handle edit auto reply
     */
    private function handleEditAutoReply($chatId, $messageId)
    {
        // Set state for auto reply editing
        $this->fileService->writeFile(ADMIN_PATH . 'edit_auto_reply_state.txt', $chatId);

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• إلغاء •', 'callback_data' => 'admin_messages']]
        ]);

        return $this->api->editMessageText($chatId, $messageId,
            "أرسل رسالة الرد التلقائي الجديدة:", [
            'reply_markup' => $keyboard
        ]);
    }
}
