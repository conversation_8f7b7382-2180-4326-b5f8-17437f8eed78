<?php
/**
 * Bot Configuration File
 *
 * This file contains all the configuration settings for the Telegram bot.
 * Make sure to update the bot token and other settings according to your needs.
 */

// Prevent multiple inclusions
if (defined('BOT_CONFIG_LOADED')) {
    return $config ?? [];
}
define('BOT_CONFIG_LOADED', true);

// Bot Configuration
if (!defined('BOT_TOKEN')) {
    define('BOT_TOKEN', '7961369878:AAGSib5Mpgf6ZeWpFLO0Ub0JfDxUHcRRjGs');
}
if (!defined('API_URL')) {
    define('API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN . '/');
}

// Admin Configuration
if (!defined('MAIN_ADMIN_ID')) {
    define('MAIN_ADMIN_ID', 6821354657);
}
if (!defined('MAX_ADMINS')) {
    define('MAX_ADMINS', 5);
}

// Channel Configuration
if (!defined('CHANNEL_USERNAME')) {
    define('CHANNEL_USERNAME', '@VEVoGamez');
}
if (!defined('DEVELOPER_USERNAME')) {
    define('DEVELOPER_USERNAME', '@GoogleYooz');
}

// File Storage Paths
if (!defined('STORAGE_PATH')) {
    define('STORAGE_PATH', __DIR__ . '/../storage/');
}
if (!defined('USERS_PATH')) {
    define('USERS_PATH', STORAGE_PATH . 'users/');
}
if (!defined('MESSAGES_PATH')) {
    define('MESSAGES_PATH', STORAGE_PATH . 'messages/');
}
if (!defined('ADMIN_PATH')) {
    define('ADMIN_PATH', STORAGE_PATH . 'admin/');
}
if (!defined('LOGS_PATH')) {
    define('LOGS_PATH', STORAGE_PATH . 'logs/');
}

// Bot Settings
if (!defined('BOT_NAME')) {
    define('BOT_NAME', 'VEVoGamez Bot');
}
if (!defined('BOT_VERSION')) {
    define('BOT_VERSION', '2.0.0');
}
if (!defined('BOT_DESCRIPTION')) {
    define('BOT_DESCRIPTION', 'البوت 🤖 مخصص للتواصل مع فريق VEVoGamez. يمكنكم التواصل وطرح الأسئلة، كما يمكنكم طلب لعبة أو تطبيق. ❗️');
}

// Security Settings
if (!defined('MAX_MESSAGE_LENGTH')) {
    define('MAX_MESSAGE_LENGTH', 4096);
}
if (!defined('RATE_LIMIT_MESSAGES')) {
    define('RATE_LIMIT_MESSAGES', 20); // Messages per minute
}
if (!defined('RATE_LIMIT_TIME')) {
    define('RATE_LIMIT_TIME', 60); // Time window in seconds
}

// Feature Flags
if (!defined('ENABLE_SUBSCRIPTION_CHECK')) {
    define('ENABLE_SUBSCRIPTION_CHECK', true);
}
if (!defined('ENABLE_ADMIN_NOTIFICATIONS')) {
    define('ENABLE_ADMIN_NOTIFICATIONS', true);
}
if (!defined('ENABLE_MESSAGE_LOGGING')) {
    define('ENABLE_MESSAGE_LOGGING', true);
}
if (!defined('ENABLE_STATISTICS')) {
    define('ENABLE_STATISTICS', true);
}

// Default Messages
if (!defined('WELCOME_MESSAGE')) {
    define('WELCOME_MESSAGE', "اهلا بك عزيزي 🤍.\nالبوت 🤖 مخصص للتواصل مع فريق VEVoGamez. يمكنكم التواصل وطرح الأسئلة، كما يمكنكم طلب لعبة أو تطبيق. ❗️\n\nارسل رسالتك وانتظر الرد.!\n\nDev - @VEVoGamez");
}
if (!defined('AUTO_REPLY_MESSAGE')) {
    define('AUTO_REPLY_MESSAGE', 'تم استلام رسالتك وسيتم الرد عليك قريباً');
}
if (!defined('BANNED_MESSAGE')) {
    define('BANNED_MESSAGE', '❌ المعذرة لا تستطيع ارسال الرسائل انت محظور');
}
if (!defined('SUBSCRIPTION_REQUIRED_MESSAGE')) {
    define('SUBSCRIPTION_REQUIRED_MESSAGE', "عذراً يجب عليك الاشتراك في القناه\nلتستطيع استخدام البوت ⚠️ 👇🏻\n\nاشترك ❗️ثم أرسل /start《 ✔️ 》");
}

// Error Messages
if (!defined('ERROR_GENERAL')) {
    define('ERROR_GENERAL', 'حدث خطأ، يرجى المحاولة مرة أخرى');
}
if (!defined('ERROR_PERMISSION_DENIED')) {
    define('ERROR_PERMISSION_DENIED', 'ليس لديك صلاحية لتنفيذ هذا الأمر');
}
if (!defined('ERROR_USER_NOT_FOUND')) {
    define('ERROR_USER_NOT_FOUND', 'المستخدم غير موجود');
}
if (!defined('ERROR_INVALID_INPUT')) {
    define('ERROR_INVALID_INPUT', 'البيانات المدخلة غير صحيحة');
}

// Success Messages
if (!defined('SUCCESS_ADMIN_ADDED')) {
    define('SUCCESS_ADMIN_ADDED', 'تم رفع المستخدم كأدمن بنجاح ✅');
}
if (!defined('SUCCESS_ADMIN_REMOVED')) {
    define('SUCCESS_ADMIN_REMOVED', 'تم تنزيل المستخدم من الأدمنية ✅');
}
if (!defined('SUCCESS_USER_BANNED')) {
    define('SUCCESS_USER_BANNED', 'تم حظر المستخدم بنجاح ✅');
}
if (!defined('SUCCESS_USER_UNBANNED')) {
    define('SUCCESS_USER_UNBANNED', 'تم إلغاء حظر المستخدم بنجاح ✅');
}
if (!defined('SUCCESS_MESSAGE_SENT')) {
    define('SUCCESS_MESSAGE_SENT', 'تم إرسال الرسالة بنجاح ✅');
}
if (!defined('SUCCESS_BROADCAST_SENT')) {
    define('SUCCESS_BROADCAST_SENT', 'تم الإذاعة بنجاح 🎉');
}

// Database/File Configuration
$config = [
    'files' => [
        'members' => USERS_PATH . 'members.txt',
        'admins' => ADMIN_PATH . 'admins.txt',
        'banned' => USERS_PATH . 'banned.txt',
        'channels' => ADMIN_PATH . 'channels.txt',
        'settings' => ADMIN_PATH . 'settings.json',
        'statistics' => ADMIN_PATH . 'statistics.json',
        'welcome_message' => ADMIN_PATH . 'welcome.txt',
        'auto_reply' => ADMIN_PATH . 'auto_reply.txt'
    ],
    'directories' => [
        'storage' => STORAGE_PATH,
        'users' => USERS_PATH,
        'messages' => MESSAGES_PATH,
        'admin' => ADMIN_PATH,
        'logs' => LOGS_PATH,
        'temp' => STORAGE_PATH . 'temp/',
        'backups' => STORAGE_PATH . 'backups/'
    ]
];

// Initialize directories
foreach ($config['directories'] as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Initialize files with default content if they don't exist
$defaultFiles = [
    $config['files']['members'] => '',
    $config['files']['admins'] => MAIN_ADMIN_ID . "\n",
    $config['files']['banned'] => '',
    $config['files']['channels'] => '',
    $config['files']['welcome_message'] => WELCOME_MESSAGE,
    $config['files']['auto_reply'] => AUTO_REPLY_MESSAGE,
    $config['files']['settings'] => json_encode([
        'notifications_enabled' => true,
        'subscription_check' => true,
        'auto_reply_enabled' => true,
        'media_restrictions' => [
            'photos' => false,
            'videos' => false,
            'documents' => false,
            'stickers' => false,
            'voice' => false,
            'audio' => false,
            'forwards' => false
        ]
    ], JSON_PRETTY_PRINT),
    $config['files']['statistics'] => json_encode([
        'total_users' => 0,
        'total_messages_received' => 0,
        'total_messages_sent' => 0,
        'total_broadcasts' => 0,
        'bot_started' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT)
];

foreach ($defaultFiles as $file => $content) {
    if (!file_exists($file)) {
        file_put_contents($file, $content);
    }
}

return $config;
