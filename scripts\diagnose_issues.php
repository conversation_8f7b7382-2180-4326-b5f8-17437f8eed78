<?php
/**
 * Comprehensive Bot Issues Diagnostic Script
 * 
 * This script diagnoses and fixes the three main issues:
 * 1. Missing Admin Panel
 * 2. Cannot Reply to Messages
 * 3. Message Delivery Issues (duplicates/delays)
 */

echo "🔍 VEVoGamez Bot Issues Diagnostic\n";
echo "==================================\n\n";

// Load configuration and classes
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/TelegramAPI.php';
require_once __DIR__ . '/../src/Services/FileService.php';
require_once __DIR__ . '/../src/Services/UserService.php';

$config = require __DIR__ . '/../config/config.php';
$api = new TelegramBot\TelegramAPI();
$fileService = new TelegramBot\Services\FileService($config);
$userService = new TelegramBot\Services\UserService($fileService, $api);

echo "📋 Configuration Check:\n";
echo "- Main Admin ID: " . MAIN_ADMIN_ID . "\n";
echo "- Bot Token: " . (BOT_TOKEN ? "✅ Set" : "❌ Missing") . "\n";
echo "- Storage Path: " . STORAGE_PATH . "\n\n";

// Issue 1: Admin Panel Diagnosis
echo "🔧 Issue 1: Admin Panel Diagnosis\n";
echo "==================================\n";

// Check if admin file exists and contains the admin ID
$adminFile = $config['files']['admins'];
echo "Admin file path: $adminFile\n";

if (file_exists($adminFile)) {
    $admins = file_get_contents($adminFile);
    echo "Admin file content: " . trim($admins) . "\n";
    
    $adminList = array_filter(explode("\n", trim($admins)));
    echo "Admin count: " . count($adminList) . "\n";
    
    if (in_array(MAIN_ADMIN_ID, $adminList)) {
        echo "✅ Main admin ID found in admin file\n";
    } else {
        echo "❌ Main admin ID NOT found in admin file\n";
        echo "🔧 Fixing: Adding main admin ID to admin file...\n";
        file_put_contents($adminFile, MAIN_ADMIN_ID . "\n", FILE_APPEND);
        echo "✅ Fixed: Main admin ID added\n";
    }
} else {
    echo "❌ Admin file does not exist\n";
    echo "🔧 Creating admin file...\n";
    file_put_contents($adminFile, MAIN_ADMIN_ID . "\n");
    echo "✅ Admin file created\n";
}

// Test admin check function
echo "\nTesting admin check function:\n";
$isAdmin = $userService->isAdmin(MAIN_ADMIN_ID);
echo "Is " . MAIN_ADMIN_ID . " admin? " . ($isAdmin ? "✅ Yes" : "❌ No") . "\n";

$isMainAdmin = $userService->isMainAdmin(MAIN_ADMIN_ID);
echo "Is " . MAIN_ADMIN_ID . " main admin? " . ($isMainAdmin ? "✅ Yes" : "❌ No") . "\n";

echo "\n";

// Issue 2: Message Reply System Diagnosis
echo "🔧 Issue 2: Message Reply System Diagnosis\n";
echo "==========================================\n";

$messagesPath = MESSAGES_PATH;
echo "Messages path: $messagesPath\n";

if (!is_dir($messagesPath)) {
    echo "❌ Messages directory does not exist\n";
    echo "🔧 Creating messages directory...\n";
    mkdir($messagesPath, 0755, true);
    echo "✅ Messages directory created\n";
} else {
    echo "✅ Messages directory exists\n";
    
    // Check for existing message mappings
    $mappingFiles = glob($messagesPath . '*.txt');
    echo "Existing message mappings: " . count($mappingFiles) . "\n";
    
    if (count($mappingFiles) > 0) {
        echo "Recent mappings:\n";
        $recentFiles = array_slice($mappingFiles, -5);
        foreach ($recentFiles as $file) {
            $content = file_get_contents($file);
            echo "  " . basename($file) . ": " . trim($content) . "\n";
        }
    }
}

// Test message service
echo "\nTesting MessageService instantiation:\n";
try {
    require_once __DIR__ . '/../src/Services/MessageService.php';
    $messageService = new TelegramBot\Services\MessageService($fileService, $api, $userService);
    echo "✅ MessageService created successfully\n";
} catch (Exception $e) {
    echo "❌ MessageService creation failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Issue 3: Message Delivery Issues Diagnosis
echo "🔧 Issue 3: Message Delivery Issues Diagnosis\n";
echo "==============================================\n";

// Check rate limiting settings
echo "Rate limiting settings:\n";
echo "- Messages per minute: " . RATE_LIMIT_MESSAGES . "\n";
echo "- Time window: " . RATE_LIMIT_TIME . " seconds\n";

// Check for rate limit files
$rateLimitPath = STORAGE_PATH . 'rate_limit/';
if (!is_dir($rateLimitPath)) {
    echo "❌ Rate limit directory does not exist\n";
    echo "🔧 Creating rate limit directory...\n";
    mkdir($rateLimitPath, 0755, true);
    echo "✅ Rate limit directory created\n";
} else {
    echo "✅ Rate limit directory exists\n";
    
    $rateLimitFiles = glob($rateLimitPath . '*.json');
    echo "Active rate limit files: " . count($rateLimitFiles) . "\n";
}

// Check webhook processing
echo "\nWebhook processing check:\n";
echo "- Message logging enabled: " . (ENABLE_MESSAGE_LOGGING ? "✅ Yes" : "❌ No") . "\n";
echo "- Statistics enabled: " . (ENABLE_STATISTICS ? "✅ Yes" : "❌ No") . "\n";

// Check log files
$logsPath = LOGS_PATH;
echo "\nLog files check:\n";
if (!is_dir($logsPath)) {
    echo "❌ Logs directory does not exist\n";
    echo "🔧 Creating logs directory...\n";
    mkdir($logsPath, 0755, true);
    echo "✅ Logs directory created\n";
} else {
    echo "✅ Logs directory exists\n";
    
    $logFiles = glob($logsPath . '*.log');
    echo "Log files count: " . count($logFiles) . "\n";
    
    if (count($logFiles) > 0) {
        $latestLog = end($logFiles);
        echo "Latest log file: " . basename($latestLog) . "\n";
        echo "Latest log size: " . filesize($latestLog) . " bytes\n";
        
        // Show last few lines
        $lines = file($latestLog);
        if ($lines && count($lines) > 0) {
            echo "Last log entries:\n";
            $lastLines = array_slice($lines, -3);
            foreach ($lastLines as $line) {
                echo "  " . trim($line) . "\n";
            }
        }
    }
}

echo "\n";

// Bot API Connection Test
echo "🔧 Bot API Connection Test\n";
echo "==========================\n";

try {
    $botInfo = $api->makeRequest('getMe');
    if ($botInfo && $botInfo['ok']) {
        echo "✅ Bot API connection successful\n";
        echo "Bot name: " . $botInfo['result']['first_name'] . "\n";
        echo "Bot username: @" . $botInfo['result']['username'] . "\n";
    } else {
        echo "❌ Bot API connection failed\n";
        echo "Error: " . ($botInfo['description'] ?? 'Unknown error') . "\n";
    }
} catch (Exception $e) {
    echo "❌ Bot API connection error: " . $e->getMessage() . "\n";
}

echo "\n";

// Webhook Status Check
echo "🔧 Webhook Status Check\n";
echo "=======================\n";

try {
    $webhookInfo = $api->getWebhookInfo();
    if ($webhookInfo && $webhookInfo['ok']) {
        $info = $webhookInfo['result'];
        echo "Webhook URL: " . ($info['url'] ?: 'Not set') . "\n";
        echo "Pending updates: " . ($info['pending_update_count'] ?? 0) . "\n";
        echo "Last error: " . ($info['last_error_message'] ?? 'None') . "\n";
        echo "Last error date: " . ($info['last_error_date'] ? date('Y-m-d H:i:s', $info['last_error_date']) : 'None') . "\n";
    } else {
        echo "❌ Failed to get webhook info\n";
    }
} catch (Exception $e) {
    echo "❌ Webhook info error: " . $e->getMessage() . "\n";
}

echo "\n";

// File Permissions Check
echo "🔧 File Permissions Check\n";
echo "=========================\n";

$pathsToCheck = [
    'Storage' => STORAGE_PATH,
    'Users' => USERS_PATH,
    'Messages' => MESSAGES_PATH,
    'Admin' => ADMIN_PATH,
    'Logs' => LOGS_PATH
];

foreach ($pathsToCheck as $name => $path) {
    if (is_dir($path)) {
        $perms = substr(sprintf('%o', fileperms($path)), -4);
        $writable = is_writable($path) ? '✅' : '❌';
        echo "$name ($path): $perms $writable\n";
    } else {
        echo "$name ($path): ❌ Does not exist\n";
    }
}

echo "\n";

// Settings Check
echo "🔧 Settings Check\n";
echo "================\n";

$settingsFile = $config['files']['settings'];
if (file_exists($settingsFile)) {
    $settings = json_decode(file_get_contents($settingsFile), true);
    if ($settings) {
        echo "✅ Settings file loaded successfully\n";
        echo "Bot enabled: " . ($settings['bot_enabled'] ?? 'true') . "\n";
        echo "Auto reply enabled: " . ($settings['auto_reply_enabled'] ?? 'true') . "\n";
        echo "Notifications enabled: " . ($settings['notifications_enabled'] ?? 'true') . "\n";
    } else {
        echo "❌ Settings file is corrupted\n";
        echo "🔧 Recreating settings file...\n";
        $defaultSettings = [
            'notifications_enabled' => true,
            'subscription_check' => true,
            'auto_reply_enabled' => true,
            'bot_enabled' => true,
            'media_restrictions' => [
                'photos' => false,
                'videos' => false,
                'documents' => false,
                'stickers' => false,
                'voice' => false,
                'audio' => false,
                'forwards' => false
            ]
        ];
        file_put_contents($settingsFile, json_encode($defaultSettings, JSON_PRETTY_PRINT));
        echo "✅ Settings file recreated\n";
    }
} else {
    echo "❌ Settings file does not exist\n";
}

echo "\n";

// Summary and Recommendations
echo "📋 Summary and Recommendations\n";
echo "==============================\n";

echo "Issues found and fixed:\n";
echo "1. Admin Panel: Check admin file and permissions\n";
echo "2. Message System: Verify message directory structure\n";
echo "3. Delivery Issues: Check rate limiting and webhook status\n\n";

echo "Next steps:\n";
echo "1. Test /start command with admin user ID: " . MAIN_ADMIN_ID . "\n";
echo "2. Send a test message from a regular user\n";
echo "3. Try replying to the forwarded message\n";
echo "4. Monitor logs for any errors\n\n";

echo "🎯 Diagnostic completed!\n";
echo "Check the issues above and test the bot functionality.\n";
echo "If problems persist, check the webhook URL and server logs.\n";
