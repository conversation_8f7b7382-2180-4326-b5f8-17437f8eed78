# Deployment Guide

This guide covers deploying the VEVoGamez Telegram Bot to various hosting environments.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Shared Hosting Deployment](#shared-hosting-deployment)
- [VPS/Dedicated Server Deployment](#vpsdedicated-server-deployment)
- [Docker Deployment](#docker-deployment)
- [Cloud Platform Deployment](#cloud-platform-deployment)
- [SSL/HTTPS Setup](#sslhttps-setup)
- [Performance Optimization](#performance-optimization)
- [Monitoring and Maintenance](#monitoring-and-maintenance)

## Prerequisites

### System Requirements

- **PHP**: 7.4 or higher
- **Extensions**: cURL, JSON, mbstring
- **Web Server**: Apache, Nginx, or similar
- **SSL Certificate**: Required for webhook
- **Storage**: Minimum 100MB free space
- **Memory**: Minimum 128MB PHP memory limit

### Before Deployment

1. **Bot Token**: Obtain from [@BotFather](https://t.me/BotFather)
2. **Admin User ID**: Get your Telegram user ID
3. **Domain/Subdomain**: With HTTPS support
4. **Server Access**: FTP, SSH, or hosting panel access

## Shared Hosting Deployment

### Step 1: Upload Files

1. **Download/Extract** the bot files
2. **Upload** all files to your hosting account
3. **Set Directory Structure**:
   ```
   public_html/
   ├── bot/
   │   ├── config/
   │   ├── src/
   │   ├── storage/
   │   ├── public/
   │   └── docs/
   ```

### Step 2: Configure Permissions

Set proper file permissions via FTP client or hosting panel:

```bash
# Directories
chmod 755 storage/
chmod 755 storage/admin/
chmod 755 storage/users/
chmod 755 storage/messages/
chmod 755 storage/logs/

# Configuration files
chmod 644 config/config.php
chmod 644 public/webhook.php
chmod 644 public/setup.php
```

### Step 3: Configure the Bot

1. **Edit Configuration**:
   ```php
   // config/config.php
   define('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE');
   define('MAIN_ADMIN_ID', YOUR_USER_ID);
   define('CHANNEL_USERNAME', '@YourChannel');
   ```

2. **Set Webhook URL**:
   - Access: `https://yourdomain.com/bot/public/setup.php`
   - Click "Set Webhook"

### Step 4: Test Deployment

1. **Access Setup Page**: `https://yourdomain.com/bot/public/setup.php`
2. **Verify Bot Info**: Check if bot information loads correctly
3. **Test Webhook**: Send a message to your bot
4. **Check Logs**: Review `storage/logs/` for any errors

### Common Shared Hosting Issues

#### Issue: Permission Denied
```bash
# Solution: Set correct permissions
chmod -R 755 storage/
```

#### Issue: PHP Memory Limit
```php
// Add to .htaccess or php.ini
ini_set('memory_limit', '256M');
```

#### Issue: cURL Not Working
```php
// Check if cURL is enabled
if (!function_exists('curl_init')) {
    die('cURL is not available');
}
```

## VPS/Dedicated Server Deployment

### Step 1: Server Setup

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install nginx php8.1-fpm php8.1-curl php8.1-json php8.1-mbstring php8.1-xml -y

# Install Certbot for SSL
sudo apt install certbot python3-certbot-nginx -y
```

#### CentOS/RHEL
```bash
# Update system
sudo yum update -y

# Install EPEL repository
sudo yum install epel-release -y

# Install required packages
sudo yum install nginx php php-fpm php-curl php-json php-mbstring -y

# Install Certbot
sudo yum install certbot python3-certbot-nginx -y
```

### Step 2: Configure Nginx

Create `/etc/nginx/sites-available/telegram-bot`:

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /var/www/telegram-bot/public;
    index webhook.php;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Deny access to sensitive directories
    location ~ ^/(storage|config|src)/ {
        deny all;
        return 404;
    }

    # Allow only webhook and setup
    location ~ ^/(webhook|setup)\.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # Deny all other PHP files
    location ~ \.php$ {
        deny all;
        return 404;
    }

    # Logging
    access_log /var/log/nginx/telegram-bot.access.log;
    error_log /var/log/nginx/telegram-bot.error.log;
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/telegram-bot /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Step 3: Deploy Application

```bash
# Create application directory
sudo mkdir -p /var/www/telegram-bot

# Upload/clone your files
sudo git clone https://github.com/yourusername/telegram-bot.git /var/www/telegram-bot

# Set permissions
sudo chown -R www-data:www-data /var/www/telegram-bot
sudo chmod -R 755 /var/www/telegram-bot/storage
sudo chmod 644 /var/www/telegram-bot/config/config.php
```

### Step 4: Configure SSL

```bash
# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com

# Auto-renewal (add to crontab)
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### Step 5: Configure PHP-FPM

Edit `/etc/php/8.1/fpm/pool.d/www.conf`:

```ini
; Increase memory limit
php_admin_value[memory_limit] = 256M

; Set timezone
php_admin_value[date.timezone] = UTC

; Enable error logging
php_admin_value[log_errors] = On
php_admin_value[error_log] = /var/log/php/error.log
```

Restart PHP-FPM:
```bash
sudo systemctl restart php8.1-fpm
```

## Docker Deployment

### Dockerfile

Create `Dockerfile`:

```dockerfile
FROM php:8.1-fpm-alpine

# Install required extensions
RUN apk add --no-cache \
    curl-dev \
    && docker-php-ext-install curl

# Install Nginx
RUN apk add --no-cache nginx

# Copy application files
COPY . /var/www/html
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/php.ini /usr/local/etc/php/conf.d/custom.ini

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage

# Create startup script
COPY docker/start.sh /start.sh
RUN chmod +x /start.sh

EXPOSE 80

CMD ["/start.sh"]
```

### Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  telegram-bot:
    build: .
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./storage:/var/www/html/storage
      - ./config:/var/www/html/config
      - ./ssl:/etc/ssl/certs
    environment:
      - BOT_TOKEN=${BOT_TOKEN}
      - MAIN_ADMIN_ID=${MAIN_ADMIN_ID}
      - CHANNEL_USERNAME=${CHANNEL_USERNAME}
    restart: unless-stopped

  nginx-proxy:
    image: nginxproxy/nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./ssl:/etc/nginx/certs
    restart: unless-stopped
```

### Environment File

Create `.env`:

```env
BOT_TOKEN=your_bot_token_here
MAIN_ADMIN_ID=your_user_id
CHANNEL_USERNAME=@YourChannel
```

### Deploy with Docker

```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f

# Update deployment
docker-compose pull
docker-compose up -d --build
```

## Cloud Platform Deployment

### Heroku Deployment

1. **Create Heroku App**:
   ```bash
   heroku create your-bot-name
   ```

2. **Configure Environment Variables**:
   ```bash
   heroku config:set BOT_TOKEN=your_token
   heroku config:set MAIN_ADMIN_ID=your_id
   ```

3. **Create Procfile**:
   ```
   web: vendor/bin/heroku-php-nginx public/
   ```

4. **Deploy**:
   ```bash
   git push heroku main
   ```

### AWS EC2 Deployment

1. **Launch EC2 Instance** (Ubuntu 20.04 LTS)
2. **Configure Security Groups** (Allow HTTP/HTTPS)
3. **Follow VPS deployment steps**
4. **Configure Auto Scaling** (optional)

### Google Cloud Platform

1. **Create Compute Engine Instance**
2. **Configure Firewall Rules**
3. **Deploy using VPS instructions**
4. **Set up Load Balancer** (for high availability)

## SSL/HTTPS Setup

### Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d yourdomain.com

# Verify auto-renewal
sudo certbot renew --dry-run
```

### Custom SSL Certificate

If you have a custom SSL certificate:

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # ... rest of configuration
}
```

## Performance Optimization

### PHP Optimization

Edit `php.ini`:

```ini
; Memory and execution
memory_limit = 256M
max_execution_time = 30
max_input_time = 30

; File uploads
upload_max_filesize = 50M
post_max_size = 50M

; OPcache (recommended)
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
```

### Nginx Optimization

```nginx
# Enable gzip compression
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript;

# Enable caching
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req zone=api burst=20 nodelay;
```

### File System Optimization

```bash
# Use tmpfs for temporary files (if enough RAM)
echo "tmpfs /var/www/telegram-bot/storage/temp tmpfs defaults,size=100M 0 0" >> /etc/fstab

# Optimize file permissions
find /var/www/telegram-bot -type f -exec chmod 644 {} \;
find /var/www/telegram-bot -type d -exec chmod 755 {} \;
```

## Monitoring and Maintenance

### Log Monitoring

Set up log rotation:

```bash
# Create logrotate configuration
sudo tee /etc/logrotate.d/telegram-bot << EOF
/var/www/telegram-bot/storage/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
EOF
```

### Health Monitoring

Create monitoring script `scripts/monitor.sh`:

```bash
#!/bin/bash

# Check if webhook is responding
WEBHOOK_URL="https://yourdomain.com/webhook.php"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" $WEBHOOK_URL)

if [ $HTTP_CODE -ne 200 ]; then
    echo "Webhook not responding: HTTP $HTTP_CODE"
    # Send alert (email, Telegram, etc.)
fi

# Check storage space
STORAGE_USAGE=$(df /var/www/telegram-bot/storage | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $STORAGE_USAGE -gt 80 ]; then
    echo "Storage usage high: ${STORAGE_USAGE}%"
fi

# Check log file sizes
find /var/www/telegram-bot/storage/logs -name "*.log" -size +100M -exec echo "Large log file: {}" \;
```

### Automated Backups

Create backup script `scripts/backup.sh`:

```bash
#!/bin/bash

BACKUP_DIR="/backups/telegram-bot"
DATE=$(date +%Y%m%d_%H%M%S)
SOURCE_DIR="/var/www/telegram-bot"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup storage directory
tar -czf "$BACKUP_DIR/storage_$DATE.tar.gz" -C "$SOURCE_DIR" storage/

# Backup configuration
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" -C "$SOURCE_DIR" config/

# Remove old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

Add to crontab:
```bash
# Daily backup at 2 AM
0 2 * * * /path/to/scripts/backup.sh

# Health check every 5 minutes
*/5 * * * * /path/to/scripts/monitor.sh
```

### Update Deployment

Create update script `scripts/update.sh`:

```bash
#!/bin/bash

cd /var/www/telegram-bot

# Backup current version
cp -r . ../telegram-bot-backup-$(date +%Y%m%d)

# Pull updates
git pull origin main

# Set permissions
chown -R www-data:www-data .
chmod -R 755 storage/

# Restart services
systemctl reload nginx
systemctl restart php8.1-fpm

echo "Update completed successfully"
```

## Troubleshooting Deployment Issues

### Common Issues

1. **Webhook Not Working**
   - Check SSL certificate validity
   - Verify webhook URL accessibility
   - Check Nginx/Apache error logs

2. **Permission Errors**
   ```bash
   sudo chown -R www-data:www-data /var/www/telegram-bot
   sudo chmod -R 755 /var/www/telegram-bot/storage
   ```

3. **PHP Errors**
   - Check PHP error logs
   - Verify required extensions are installed
   - Check memory limits

4. **File Not Found Errors**
   - Verify file paths in configuration
   - Check directory structure
   - Ensure all files are uploaded

### Debug Mode

Enable debug mode for troubleshooting:

```php
// In config/config.php
define('DEBUG_MODE', true);
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### Testing Deployment

1. **Access Setup Page**: Verify bot information loads
2. **Test Webhook**: Send test message to bot
3. **Check Admin Panel**: Verify admin functionality
4. **Test Broadcasting**: Send test broadcast
5. **Monitor Logs**: Check for errors in logs

Remember to disable debug mode in production!
