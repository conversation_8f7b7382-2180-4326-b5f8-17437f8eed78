<?php

namespace TelegramBot\Services;

use TelegramBot\TelegramAPI;

/**
 * Message Service Class
 * 
 * Handles message forwarding, tracking, and management between users and admins.
 */
class MessageService
{
    private $fileService;
    private $api;
    private $userService;

    public function __construct(FileService $fileService, TelegramAPI $api, UserService $userService)
    {
        $this->fileService = $fileService;
        $this->api = $api;
        $this->userService = $userService;
    }

    /**
     * Forward user message to admin
     */
    public function forwardToAdmin($message, $adminChatId)
    {
        $userId = $message['from']['id'];
        $userName = $message['from']['first_name'] ?? 'Unknown';
        $messageId = $message['message_id'];
        $chatId = $message['chat']['id'];

        // Create header message
        $headerText = "رسالة من: [$userName](tg://user?id=$userId)\nالمعرف: `$userId`";
        
        $headerResult = $this->api->sendMessage($adminChatId, $headerText, [
            'parse_mode' => 'Markdown',
            'disable_web_page_preview' => true
        ]);

        if (!$headerResult || !$headerResult['ok']) {
            return false;
        }

        // Forward the actual message
        $forwardResult = $this->api->forwardMessage($adminChatId, $chatId, $messageId);
        
        if (!$forwardResult || !$forwardResult['ok']) {
            return false;
        }

        // Store message mapping for replies
        $adminMessageId = $forwardResult['result']['message_id'];
        $mappingData = "$chatId=$userName=$messageId";

        // Create mapping file with consistent naming
        $mappingFileName = MESSAGES_PATH . ($adminMessageId - 1) . '.txt';
        $this->fileService->writeFile($mappingFileName, $mappingData);

        // Enhanced logging for debugging
        $this->fileService->logMessage('info', "Message mapping created", [
            'user_id' => $userId,
            'user_chat_id' => $chatId,
            'user_message_id' => $messageId,
            'admin_message_id' => $adminMessageId,
            'mapping_file' => basename($mappingFileName),
            'mapping_data' => $mappingData
        ]);

        // Update statistics
        $this->fileService->incrementStatistic('total_messages_received');

        // Log message forwarding
        $this->fileService->logMessage('info', "Message forwarded from user $userId to admin", [
            'user_id' => $userId,
            'admin_chat_id' => $adminChatId,
            'message_id' => $messageId
        ]);

        return true;
    }

    /**
     * Send reply from admin to user
     */
    public function sendReplyToUser($adminMessage, $replyToMessageId)
    {
        // Get original message mapping - check both possible file names
        $mappingFile1 = MESSAGES_PATH . ($replyToMessageId - 1) . '.txt';
        $mappingFile2 = MESSAGES_PATH . $replyToMessageId . '.txt';

        $mappingFile = null;
        if (file_exists($mappingFile1)) {
            $mappingFile = $mappingFile1;
        } elseif (file_exists($mappingFile2)) {
            $mappingFile = $mappingFile2;
        }

        if (!$mappingFile) {
            // Enhanced error logging
            $this->fileService->logMessage('error', "Message mapping not found", [
                'reply_to_message_id' => $replyToMessageId,
                'checked_files' => [$mappingFile1, $mappingFile2],
                'messages_dir_exists' => is_dir(MESSAGES_PATH),
                'messages_dir_writable' => is_writable(MESSAGES_PATH),
                'existing_files' => glob(MESSAGES_PATH . '*.txt')
            ]);

            return ['success' => false, 'message' => 'لم يتم العثور على الرسالة الأصلية. الملف: ' . basename($mappingFile1) . ' أو ' . basename($mappingFile2)];
        }

        $mapping = explode('=', $this->fileService->readFile($mappingFile));
        if (count($mapping) < 3) {
            return ['success' => false, 'message' => 'بيانات الرسالة غير صحيحة'];
        }

        $userChatId = $mapping[0];
        $userName = $mapping[1];
        $originalMessageId = $mapping[2];

        // Send reply to user
        $result = null;
        
        if (isset($adminMessage['text'])) {
            // Text message
            $result = $this->api->sendMessage($userChatId, $adminMessage['text'], [
                'reply_to_message_id' => $originalMessageId
            ]);
        } else {
            // Media message
            $result = $this->forwardMediaMessage($adminMessage, $userChatId, $originalMessageId);
        }

        if (!$result || !$result['ok']) {
            return ['success' => false, 'message' => 'فشل في إرسال الرسالة'];
        }

        // Store reply mapping for editing/deleting
        $replyMessageId = $result['result']['message_id'];
        $replyMappingData = "$userChatId=$replyMessageId=$userName";
        
        $this->fileService->writeFile(
            MESSAGES_PATH . $replyMessageId . '.txt',
            $replyMappingData
        );

        // Update statistics
        $this->fileService->incrementStatistic('total_messages_sent');

        // Log reply
        $this->fileService->logMessage('info', "Reply sent to user $userChatId", [
            'user_id' => $userChatId,
            'admin_message_id' => $adminMessage['message_id'],
            'reply_message_id' => $replyMessageId
        ]);

        return [
            'success' => true, 
            'message' => "تم الإرسال لـ [$userName](tg://user?id=$userChatId) بنجاح ✅",
            'reply_message_id' => $replyMessageId
        ];
    }

    /**
     * Forward media message
     */
    private function forwardMediaMessage($message, $chatId, $replyToMessageId)
    {
        $messageType = $this->getMessageType($message);
        
        switch ($messageType) {
            case 'photo':
                $fileId = end($message['photo'])['file_id'];
                return $this->api->sendPhoto($chatId, $fileId, $message['caption'] ?? '', [
                    'reply_to_message_id' => $replyToMessageId
                ]);
                
            case 'video':
                return $this->api->sendVideo($chatId, $message['video']['file_id'], $message['caption'] ?? '', [
                    'reply_to_message_id' => $replyToMessageId
                ]);
                
            case 'document':
                return $this->api->sendDocument($chatId, $message['document']['file_id'], $message['caption'] ?? '', [
                    'reply_to_message_id' => $replyToMessageId
                ]);
                
            case 'audio':
                return $this->api->sendAudio($chatId, $message['audio']['file_id'], $message['caption'] ?? '', [
                    'reply_to_message_id' => $replyToMessageId
                ]);
                
            case 'voice':
                return $this->api->sendVoice($chatId, $message['voice']['file_id'], $message['caption'] ?? '', [
                    'reply_to_message_id' => $replyToMessageId
                ]);
                
            case 'sticker':
                return $this->api->sendSticker($chatId, $message['sticker']['file_id'], [
                    'reply_to_message_id' => $replyToMessageId
                ]);
                
            default:
                return false;
        }
    }

    /**
     * Get message type
     */
    private function getMessageType($message)
    {
        $types = ['photo', 'video', 'document', 'audio', 'voice', 'sticker'];
        
        foreach ($types as $type) {
            if (isset($message[$type])) {
                return $type;
            }
        }
        
        return 'text';
    }

    /**
     * Edit message
     */
    public function editMessage($messageId, $newText, $chatId)
    {
        // Get message mapping
        $mappingFile = MESSAGES_PATH . $messageId . '.txt';
        
        if (!file_exists($mappingFile)) {
            return ['success' => false, 'message' => 'لم يتم العثور على الرسالة'];
        }

        $mapping = explode('=', $this->fileService->readFile($mappingFile));
        $userChatId = $mapping[0];
        $userMessageId = $mapping[1];
        $userName = $mapping[2];

        // Edit the message
        $result = $this->api->editMessageText($userChatId, $userMessageId, $newText);
        
        if (!$result || !$result['ok']) {
            return ['success' => false, 'message' => 'فشل في تعديل الرسالة'];
        }

        // Log edit
        $this->fileService->logMessage('info', "Message edited for user $userChatId", [
            'user_id' => $userChatId,
            'message_id' => $userMessageId,
            'new_text' => $newText
        ]);

        return [
            'success' => true,
            'message' => "تم تعديل الرسالة لـ [$userName](tg://user?id=$userChatId) بنجاح ✅"
        ];
    }

    /**
     * Delete message
     */
    public function deleteMessage($messageId)
    {
        // Get message mapping
        $mappingFile = MESSAGES_PATH . $messageId . '.txt';
        
        if (!file_exists($mappingFile)) {
            return ['success' => false, 'message' => 'لم يتم العثور على الرسالة'];
        }

        $mapping = explode('=', $this->fileService->readFile($mappingFile));
        $userChatId = $mapping[0];
        $userMessageId = $mapping[1];
        $userName = $mapping[2];

        // Delete the message
        $result = $this->api->deleteMessage($userChatId, $userMessageId);
        
        if (!$result || !$result['ok']) {
            return ['success' => false, 'message' => 'فشل في حذف الرسالة'];
        }

        // Remove mapping file
        $this->fileService->deleteFile($mappingFile);

        // Log deletion
        $this->fileService->logMessage('info', "Message deleted for user $userChatId", [
            'user_id' => $userChatId,
            'message_id' => $userMessageId
        ]);

        return [
            'success' => true,
            'message' => "تم حذف الرسالة لـ [$userName](tg://user?id=$userChatId) بنجاح ✅"
        ];
    }



    /**
     * Send auto reply to user
     */
    public function sendAutoReply($chatId, $messageId)
    {
        $autoReply = $this->fileService->getAutoReplyMessage();

        if (empty($autoReply)) {
            return false;
        }

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => 'أشترك في القناة ليصلك التحديثات', 'url' => 't.me/VEVoGamez']]
        ]);

        return $this->api->sendMessage($chatId, $autoReply, [
            'reply_to_message_id' => $messageId,
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Check if media type is allowed
     */
    public function isMediaAllowed($messageType)
    {
        $settings = $this->fileService->getSettings();
        $restrictions = $settings['media_restrictions'] ?? [];
        
        return !($restrictions[$messageType] ?? false);
    }

    /**
     * Handle media restriction
     */
    public function handleMediaRestriction($chatId, $messageId, $mediaType)
    {
        $messages = [
            'photos' => 'عذراً، استقبال الصور مغلق ❌',
            'videos' => 'عذراً، استقبال الفيديو مغلق ❌',
            'documents' => 'عذراً، استقبال الملفات مغلق ❌',
            'stickers' => 'عذراً، استقبال الملصقات مغلق ❌',
            'voice' => 'عذراً، استقبال الصوتيات مغلق ❌',
            'audio' => 'عذراً، استقبال الموسيقى مغلق ❌',
            'forwards' => 'عذراً، استقبال التوجيه مغلق ❌'
        ];

        $message = $messages[$mediaType] ?? 'نوع الملف غير مدعوم';
        
        return $this->api->sendMessage($chatId, $message, [
            'reply_to_message_id' => $messageId
        ]);
    }

    /**
     * Clean old message mappings
     */
    public function cleanOldMappings($days = 7)
    {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $mappingFiles = glob(MESSAGES_PATH . '*.txt');
        
        foreach ($mappingFiles as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
            }
        }
    }

    /**
     * Get message statistics
     */
    public function getMessageStatistics()
    {
        $stats = $this->fileService->getStatistics();
        
        return [
            'total_received' => $stats['total_messages_received'] ?? 0,
            'total_sent' => $stats['total_messages_sent'] ?? 0,
            'pending_mappings' => count(glob(MESSAGES_PATH . '*.txt'))
        ];
    }
}
