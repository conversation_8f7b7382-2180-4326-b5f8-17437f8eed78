<?php

namespace TelegramBot\Controllers;

use TelegramBot\TelegramAPI;
use TelegramBot\Services\FileService;
use TelegramBot\Services\UserService;
use TelegramBot\Services\MessageService;
use TelegramBot\Services\ValidationService;

/**
 * User Controller Class
 * 
 * Handles user interactions, message processing, and user-facing commands.
 */
class UserController
{
    private $api;
    private $fileService;
    private $userService;
    private $messageService;
    private $validationService;

    public function __construct(
        TelegramAPI $api, 
        FileService $fileService, 
        UserService $userService, 
        MessageService $messageService,
        ValidationService $validationService
    ) {
        $this->api = $api;
        $this->fileService = $fileService;
        $this->userService = $userService;
        $this->messageService = $messageService;
        $this->validationService = $validationService;
    }

    /**
     * Handle /start command
     */
    public function handleStart($message)
    {
        $userId = $message['from']['id'];
        $chatId = $message['chat']['id'];
        $userName = $message['from']['first_name'] ?? 'مستخدم';

        // Log start command for debugging
        $this->fileService->logMessage('info', "Start command received from user $userId", [
            'user_id' => $userId,
            'username' => $userName
        ]);

        // Check if user is banned
        if ($this->userService->isBanned($userId)) {
            return $this->api->sendMessage($chatId, BANNED_MESSAGE);
        }

        // Register user if new
        if (!$this->userService->isMember($userId)) {
            $this->userService->registerUser($userId, [
                'first_name' => $userName,
                'username' => $message['from']['username'] ?? '',
                'chat_type' => $message['chat']['type']
            ]);

            // Send new user notification to admin if enabled
            $this->sendNewUserNotification($userId, $userName);
        }

        // Check if user is admin (with detailed logging)
        $isAdmin = $this->userService->isAdmin($userId);
        $isMainAdmin = $this->userService->isMainAdmin($userId);

        $this->fileService->logMessage('info', "Admin check for user $userId", [
            'user_id' => $userId,
            'is_admin' => $isAdmin,
            'is_main_admin' => $isMainAdmin,
            'main_admin_id' => MAIN_ADMIN_ID
        ]);

        if ($isAdmin || $isMainAdmin) {
            $this->fileService->logMessage('info', "Sending admin welcome to user $userId");
            return $this->sendAdminWelcome($chatId);
        }

        // Check subscription if enabled (for non-admins)
        if (ENABLE_SUBSCRIPTION_CHECK && !$this->checkSubscription($userId)) {
            return $this->sendSubscriptionMessage($chatId, $message['message_id']);
        }

        // Send regular user welcome
        return $this->sendUserWelcome($chatId, $message['from']['username'] ?? null);
    }

    /**
     * Send admin welcome message
     */
    private function sendAdminWelcome($chatId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => 'اخر تحديثات البوت 🧬', 'url' => 't.me/VEVoGamez']],
            [
                ['text' => 'عمل البوت', 'callback_data' => 'admin_bot_status'],
                ['text' => 'اشعارات الدخول', 'callback_data' => 'admin_notifications']
            ],
            [['text' => 'قسم الاشتراك الاجباري', 'callback_data' => 'admin_subscription']],
            [['text' => 'قسم الاذاعة', 'callback_data' => 'admin_broadcast']],
            [['text' => 'قسم الاحصائيات', 'callback_data' => 'admin_statistics']],
            [['text' => 'قسم الادمنية', 'callback_data' => 'admin_management']]
        ]);

        $text = "*• اهلا بك في لوحة الأدمن الخاصة بالبوت 🤖\n\n- يمكنك التحكم في البوت الخاص بك من هنا*";

        return $this->api->sendMessage($chatId, $text, [
            'reply_markup' => $keyboard,
            'parse_mode' => 'Markdown'
        ]);
    }

    /**
     * Send user welcome message
     */
    private function sendUserWelcome($chatId, $username = null)
    {
        $welcomeMessage = $this->fileService->getWelcomeMessage();
        
        $keyboard = TelegramAPI::createInlineKeyboard([
            [
                ['text' => 'قناة البوت', 'url' => 't.me/VEVoGamez'],
                ['text' => 'Yooz Dev', 'url' => 't.me/GoogleYooz']
            ]
        ]);

        if ($username) {
            return $this->api->sendPhoto($chatId, 'https://t.me/vevogamerz', [
                'caption' => $welcomeMessage,
                'parse_mode' => 'Markdown',
                'reply_markup' => $keyboard
            ]);
        } else {
            return $this->api->sendMessage($chatId, $welcomeMessage, [
                'parse_mode' => 'Markdown',
                'reply_markup' => $keyboard
            ]);
        }
    }

    /**
     * Handle user message
     */
    public function handleUserMessage($message)
    {
        $userId = $message['from']['id'];
        $chatId = $message['chat']['id'];
        $text = $message['text'] ?? '';

        // Check if user is banned
        if ($this->userService->isBanned($userId)) {
            return $this->api->sendMessage($chatId, BANNED_MESSAGE);
        }

        // Check rate limiting
        if (!$this->userService->checkRateLimit($userId)) {
            return $this->api->sendMessage($chatId, 'تم تجاوز الحد المسموح من الرسائل، يرجى المحاولة لاحقاً');
        }

        // Check subscription if enabled
        if (ENABLE_SUBSCRIPTION_CHECK && !$this->checkSubscription($userId)) {
            return $this->sendSubscriptionMessage($chatId, $message['message_id']);
        }

        // Handle special commands
        if ($this->handleSpecialCommands($message)) {
            return true;
        }

        // Check if message should be forwarded to admin
        if ($this->shouldForwardMessage($message)) {
            return $this->forwardMessageToAdmin($message);
        }

        return false;
    }

    /**
     * Handle special user commands
     */
    private function handleSpecialCommands($message)
    {
        $text = $message['text'] ?? '';
        $userId = $message['from']['id'];
        $chatId = $message['chat']['id'];

        switch ($text) {
            case 'ارفعني':
                return $this->handlePromotionRequest($message);
                
            case 'الادمنية':
                if ($this->userService->isMainAdmin($userId)) {
                    return $this->showAdminList($chatId);
                }
                break;
                
            default:
                // Check if it's a promotion code
                if ($this->validationService->validatePromotionCode($text)) {
                    return $this->handlePromotionCode($message);
                }
                
                // Check if it's an admin command
                if ($this->userService->isAdmin($userId)) {
                    return $this->handleAdminCommand($message);
                }
        }

        return false;
    }

    /**
     * Handle promotion request
     */
    private function handlePromotionRequest($message)
    {
        $userId = $message['from']['id'];
        $chatId = $message['chat']['id'];

        if ($this->userService->isMainAdmin($userId)) {
            return false; // Main admin doesn't need promotion
        }

        if ($this->userService->isBanned($userId)) {
            return $this->api->sendMessage($chatId, 
                '❌ المعذرة لا استطيع ترقيتك لانك محظور من قبل مالك البوت',
                ['reply_to_message_id' => $message['message_id']]
            );
        }

        if ($this->userService->isAdmin($userId)) {
            return $this->api->sendMessage($chatId,
                'لقد تمت ترقيتك بالفعل مسبقاً ✅',
                ['reply_to_message_id' => $message['message_id']]
            );
        }

        // Set state for promotion code input
        $this->fileService->writeFile(STORAGE_PATH . "states/$userId.txt", 'awaiting_promotion_code');

        return $this->api->sendMessage($chatId,
            "اهلا بك عزيزي\nاذا كنت تريد ان يقوم البوت برفعك ادمن قم بارسال رمز الترقية المكون من 6 ارقام",
            ['reply_to_message_id' => $message['message_id']]
        );
    }

    /**
     * Handle promotion code
     */
    private function handlePromotionCode($message)
    {
        $userId = $message['from']['id'];
        $chatId = $message['chat']['id'];
        $code = $message['text'];

        // Check if user is awaiting promotion code
        $userState = $this->fileService->readFile(STORAGE_PATH . "states/$userId.txt");
        if (trim($userState) !== 'awaiting_promotion_code') {
            return false;
        }

        // Clear state
        $this->fileService->deleteFile(STORAGE_PATH . "states/$userId.txt");

        // Verify and promote
        $result = $this->userService->promoteWithCode($userId, $code);

        if ($result['success']) {
            // Notify main admin
            $userName = $message['from']['first_name'] ?? 'مستخدم';
            $this->api->sendMessage(MAIN_ADMIN_ID,
                "✅ لقد تمت ترقية العضو\n- [$userName](tg://user?id=$userId)",
                ['parse_mode' => 'Markdown']
            );
        }

        return $this->api->sendMessage($chatId, $result['message']);
    }

    /**
     * Handle admin commands
     */
    private function handleAdminCommand($message)
    {
        $text = $message['text'] ?? '';
        $userId = $message['from']['id'];
        $chatId = $message['chat']['id'];

        // Generate promotion code
        if ($text === 'ترقية' && $this->userService->isMainAdmin($userId)) {
            $code = $this->userService->generatePromotionCode();
            
            return $this->api->sendMessage($chatId,
                "اهلا عزيزي المطور لرفع اي شخص ليكون ادمن قم بارسال هذا الرمز له\nرمز الترقية: `$code`",
                [
                    'parse_mode' => 'Markdown',
                    'reply_to_message_id' => $message['message_id']
                ]
            );
        }

        // Remove admin command
        if (preg_match('/^تنزيل (.+)/', $text, $matches) && $this->userService->isMainAdmin($userId)) {
            $targetUserId = trim($matches[1]);
            
            if (!$this->validationService->validateUserId($targetUserId)) {
                return $this->api->sendMessage($chatId, 'معرف المستخدم غير صحيح');
            }

            $result = $this->userService->removeAdmin($targetUserId, $userId);
            
            if ($result['success']) {
                // Notify the removed admin
                $this->api->sendMessage($targetUserId, 'تم تنزيلك من الأدمنية ❌');
            }

            return $this->api->sendMessage($chatId, $result['message'], [
                'reply_to_message_id' => $message['message_id']
            ]);
        }

        return false;
    }

    /**
     * Check if message should be forwarded to admin
     */
    private function shouldForwardMessage($message)
    {
        $text = $message['text'] ?? '';
        
        // Skip certain commands and messages
        $skipMessages = ['/start', 'ارفعني', 'الادمنية'];
        
        if (in_array($text, $skipMessages)) {
            return false;
        }

        // Skip if user is admin
        if ($this->userService->isAdmin($message['from']['id'])) {
            return false;
        }

        // Skip if chat is not private
        if ($message['chat']['type'] !== 'private') {
            return false;
        }

        return true;
    }

    /**
     * Forward message to admin
     */
    private function forwardMessageToAdmin($message)
    {
        $settings = $this->fileService->getSettings();
        
        // Check if bot is enabled
        if (!($settings['bot_enabled'] ?? true)) {
            return $this->api->sendMessage($message['chat']['id'],
                'تم إيقاف استقبال الرسائل من قبل مالك البوت',
                ['reply_to_message_id' => $message['message_id']]
            );
        }

        // Check media restrictions
        $messageType = $this->getMessageType($message);
        if ($messageType !== 'text' && !$this->messageService->isMediaAllowed($messageType)) {
            return $this->messageService->handleMediaRestriction(
                $message['chat']['id'],
                $message['message_id'],
                $messageType
            );
        }

        // Forward to admin
        $adminChatId = MAIN_ADMIN_ID; // You can make this configurable
        $result = $this->messageService->forwardToAdmin($message, $adminChatId);

        if ($result) {
            // Send auto-reply if enabled
            if ($settings['auto_reply_enabled'] ?? true) {
                $this->messageService->sendAutoReply($message['chat']['id'], $message['message_id']);
            }
            return true;
        }

        return false;
    }

    /**
     * Get message type
     */
    private function getMessageType($message)
    {
        $types = ['photo', 'video', 'document', 'audio', 'voice', 'sticker'];
        
        foreach ($types as $type) {
            if (isset($message[$type])) {
                return $type . 's'; // Convert to plural for settings
            }
        }
        
        if (isset($message['forward_from']) || isset($message['forward_from_chat'])) {
            return 'forwards';
        }
        
        return 'text';
    }

    /**
     * Check user subscription to required channels
     */
    private function checkSubscription($userId)
    {
        $channels = $this->fileService->getChannels();
        
        if (empty($channels)) {
            return true; // No subscription required
        }

        foreach ($channels as $channel) {
            if (empty($channel)) continue;
            
            $result = $this->api->getChatMember($channel, $userId);
            
            if (!$result || !$result['ok']) {
                return false;
            }

            $status = $result['result']['status'];
            if (in_array($status, ['left', 'kicked'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Send subscription required message
     */
    private function sendSubscriptionMessage($chatId, $messageId)
    {
        $channels = $this->fileService->getChannels();
        $buttons = [];

        foreach ($channels as $channel) {
            if (empty($channel)) continue;
            
            $channelName = str_replace('@', '', $channel);
            $buttons[] = [['text' => "قناة البوت", 'url' => "https://t.me/$channelName"]];
        }

        $keyboard = TelegramAPI::createInlineKeyboard($buttons);

        return $this->api->sendMessage($chatId, SUBSCRIPTION_REQUIRED_MESSAGE, [
            'reply_to_message_id' => $messageId,
            'parse_mode' => 'Markdown',
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Send new user notification to admin
     */
    private function sendNewUserNotification($userId, $userName)
    {
        $settings = $this->fileService->getSettings();
        
        if (!($settings['notifications_enabled'] ?? true)) {
            return;
        }

        $stats = $this->userService->getUserStatistics();
        $text = "٭ تم دخول شخص جديد الى البوت الخاص بك 👾\n";
        $text .= "-----------------------\n";
        $text .= "• معلومات العضو الجديد:\n\n";
        $text .= "• الاسم: [$userName](tg://user?id=$userId)\n";
        $text .= "• الايدي: `$userId`\n";
        $text .= "-----------------------\n";
        $text .= "• عدد الاعضاء الكلي: " . $stats['total_users'];

        $this->api->sendMessage(MAIN_ADMIN_ID, $text, [
            'parse_mode' => 'Markdown',
            'disable_web_page_preview' => true
        ]);
    }

    /**
     * Show admin list (for main admin only)
     */
    private function showAdminList($chatId)
    {
        $admins = $this->userService->getAdminList();
        
        $text = "ℹ هذه هي قائمة الأدمنية\n\n";
        
        if (empty($admins)) {
            $text .= "لا يوجد أدمنية حالياً";
        } else {
            foreach ($admins as $index => $admin) {
                $num = $index + 1;
                $name = $admin['first_name'];
                $id = $admin['id'];
                $status = $admin['is_main_admin'] ? '👑' : '👮‍♀️';
                
                $text .= "$num. $status [$name](tg://user?id=$id) `$id`\n";
            }
        }

        return $this->api->sendMessage($chatId, $text, [
            'parse_mode' => 'Markdown',
            'disable_web_page_preview' => true
        ]);
    }
}
