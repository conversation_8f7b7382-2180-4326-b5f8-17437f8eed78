<?php

namespace TelegramBot\Utils;

/**
 * Advanced Logger Utility Class
 * 
 * Provides enhanced logging capabilities with different levels and formats.
 */
class Logger
{
    const LEVEL_DEBUG = 0;
    const LEVEL_INFO = 1;
    const LEVEL_WARNING = 2;
    const LEVEL_ERROR = 3;
    const LEVEL_CRITICAL = 4;

    private $logPath;
    private $minLevel;
    private $maxFileSize;
    private $maxFiles;

    public function __construct($logPath = null, $minLevel = self::LEVEL_INFO)
    {
        $this->logPath = $logPath ?: LOGS_PATH;
        $this->minLevel = $minLevel;
        $this->maxFileSize = 10 * 1024 * 1024; // 10MB
        $this->maxFiles = 30; // Keep 30 files
        
        $this->ensureLogDirectory();
    }

    /**
     * Ensure log directory exists
     */
    private function ensureLogDirectory()
    {
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }

    /**
     * Log debug message
     */
    public function debug($message, $context = [])
    {
        $this->log(self::LEVEL_DEBUG, $message, $context);
    }

    /**
     * Log info message
     */
    public function info($message, $context = [])
    {
        $this->log(self::LEVEL_INFO, $message, $context);
    }

    /**
     * Log warning message
     */
    public function warning($message, $context = [])
    {
        $this->log(self::LEVEL_WARNING, $message, $context);
    }

    /**
     * Log error message
     */
    public function error($message, $context = [])
    {
        $this->log(self::LEVEL_ERROR, $message, $context);
    }

    /**
     * Log critical message
     */
    public function critical($message, $context = [])
    {
        $this->log(self::LEVEL_CRITICAL, $message, $context);
    }

    /**
     * Main logging method
     */
    public function log($level, $message, $context = [])
    {
        if ($level < $this->minLevel) {
            return;
        }

        $levelName = $this->getLevelName($level);
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
        
        $logEntry = "[{$timestamp}] [{$levelName}] {$message}{$contextStr}\n";
        
        $this->writeToFile($logEntry, $level);
    }

    /**
     * Write log entry to file
     */
    private function writeToFile($logEntry, $level)
    {
        $filename = $this->getLogFilename($level);
        
        // Check file size and rotate if necessary
        if (file_exists($filename) && filesize($filename) > $this->maxFileSize) {
            $this->rotateLogFile($filename);
        }
        
        file_put_contents($filename, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Get log filename based on level and date
     */
    private function getLogFilename($level)
    {
        $date = date('Y-m-d');
        $levelName = strtolower($this->getLevelName($level));
        
        // Separate critical and error logs
        if ($level >= self::LEVEL_ERROR) {
            return $this->logPath . "error-{$date}.log";
        }
        
        return $this->logPath . "{$date}.log";
    }

    /**
     * Get level name from level constant
     */
    private function getLevelName($level)
    {
        $levels = [
            self::LEVEL_DEBUG => 'DEBUG',
            self::LEVEL_INFO => 'INFO',
            self::LEVEL_WARNING => 'WARNING',
            self::LEVEL_ERROR => 'ERROR',
            self::LEVEL_CRITICAL => 'CRITICAL'
        ];
        
        return $levels[$level] ?? 'UNKNOWN';
    }

    /**
     * Rotate log file when it gets too large
     */
    private function rotateLogFile($filename)
    {
        $rotatedFilename = $filename . '.' . time();
        rename($filename, $rotatedFilename);
        
        // Compress old log file
        if (function_exists('gzencode')) {
            $content = file_get_contents($rotatedFilename);
            file_put_contents($rotatedFilename . '.gz', gzencode($content));
            unlink($rotatedFilename);
        }
        
        $this->cleanOldLogFiles();
    }

    /**
     * Clean old log files
     */
    private function cleanOldLogFiles()
    {
        $files = glob($this->logPath . '*.log*');
        
        if (count($files) > $this->maxFiles) {
            // Sort by modification time
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // Remove oldest files
            $filesToRemove = array_slice($files, 0, count($files) - $this->maxFiles);
            foreach ($filesToRemove as $file) {
                unlink($file);
            }
        }
    }

    /**
     * Log API request
     */
    public function logApiRequest($method, $data, $response, $duration)
    {
        $context = [
            'method' => $method,
            'data_size' => strlen(json_encode($data)),
            'response_ok' => $response['ok'] ?? false,
            'duration_ms' => round($duration * 1000, 2),
            'memory_usage' => memory_get_usage(true)
        ];
        
        if (!($response['ok'] ?? false)) {
            $context['error'] = $response['description'] ?? 'Unknown error';
            $this->warning("API request failed: {$method}", $context);
        } else {
            $this->debug("API request: {$method}", $context);
        }
    }

    /**
     * Log user action
     */
    public function logUserAction($userId, $action, $details = [])
    {
        $context = array_merge([
            'user_id' => $userId,
            'action' => $action,
            'ip' => Helper::getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ], $details);
        
        $this->info("User action: {$action}", $context);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent($event, $userId = null, $details = [])
    {
        $context = array_merge([
            'event' => $event,
            'user_id' => $userId,
            'ip' => Helper::getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => time()
        ], $details);
        
        $this->warning("Security event: {$event}", $context);
        
        // Also log to separate security log
        $this->writeToSecurityLog($event, $context);
    }

    /**
     * Write to separate security log
     */
    private function writeToSecurityLog($event, $context)
    {
        $filename = $this->logPath . 'security-' . date('Y-m-d') . '.log';
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = json_encode($context, JSON_UNESCAPED_UNICODE);
        
        $logEntry = "[{$timestamp}] SECURITY: {$event} {$contextStr}\n";
        file_put_contents($filename, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Log performance metrics
     */
    public function logPerformance($operation, $duration, $memoryUsage = null)
    {
        $context = [
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'memory_usage' => $memoryUsage ?: memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ];
        
        if ($duration > 1.0) { // Log slow operations
            $this->warning("Slow operation: {$operation}", $context);
        } else {
            $this->debug("Performance: {$operation}", $context);
        }
    }

    /**
     * Log exception
     */
    public function logException($exception, $context = [])
    {
        $exceptionContext = array_merge([
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ], $context);
        
        $this->error("Exception: " . $exception->getMessage(), $exceptionContext);
    }

    /**
     * Get log statistics
     */
    public function getLogStatistics($days = 7)
    {
        $stats = [
            'total_entries' => 0,
            'by_level' => [],
            'by_date' => [],
            'file_sizes' => []
        ];
        
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        $endDate = date('Y-m-d');
        
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $logFile = $this->logPath . "{$currentDate}.log";
            
            if (file_exists($logFile)) {
                $content = file_get_contents($logFile);
                $lines = explode("\n", $content);
                
                $stats['by_date'][$currentDate] = count($lines) - 1; // -1 for empty last line
                $stats['total_entries'] += count($lines) - 1;
                $stats['file_sizes'][$currentDate] = filesize($logFile);
                
                // Count by level
                foreach ($lines as $line) {
                    if (preg_match('/\[(DEBUG|INFO|WARNING|ERROR|CRITICAL)\]/', $line, $matches)) {
                        $level = $matches[1];
                        $stats['by_level'][$level] = ($stats['by_level'][$level] ?? 0) + 1;
                    }
                }
            } else {
                $stats['by_date'][$currentDate] = 0;
                $stats['file_sizes'][$currentDate] = 0;
            }
            
            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
        }
        
        return $stats;
    }

    /**
     * Search logs
     */
    public function searchLogs($query, $days = 7, $level = null)
    {
        $results = [];
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        $endDate = date('Y-m-d');
        
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $logFile = $this->logPath . "{$currentDate}.log";
            
            if (file_exists($logFile)) {
                $content = file_get_contents($logFile);
                $lines = explode("\n", $content);
                
                foreach ($lines as $lineNumber => $line) {
                    if (empty($line)) continue;
                    
                    // Filter by level if specified
                    if ($level && !preg_match("/\[{$level}\]/", $line)) {
                        continue;
                    }
                    
                    // Search for query
                    if (stripos($line, $query) !== false) {
                        $results[] = [
                            'file' => $logFile,
                            'line_number' => $lineNumber + 1,
                            'content' => $line,
                            'date' => $currentDate
                        ];
                    }
                }
            }
            
            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
        }
        
        return $results;
    }

    /**
     * Export logs as CSV
     */
    public function exportLogsAsCsv($days = 7, $level = null)
    {
        $csv = "Date,Time,Level,Message,Context\n";
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        $endDate = date('Y-m-d');
        
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $logFile = $this->logPath . "{$currentDate}.log";
            
            if (file_exists($logFile)) {
                $content = file_get_contents($logFile);
                $lines = explode("\n", $content);
                
                foreach ($lines as $line) {
                    if (empty($line)) continue;
                    
                    if (preg_match('/\[([^\]]+)\] \[([^\]]+)\] (.+)/', $line, $matches)) {
                        $timestamp = $matches[1];
                        $logLevel = $matches[2];
                        $message = $matches[3];
                        
                        // Filter by level if specified
                        if ($level && $logLevel !== $level) {
                            continue;
                        }
                        
                        // Split message and context
                        $parts = explode(' {', $message, 2);
                        $messageText = $parts[0];
                        $context = isset($parts[1]) ? '{' . $parts[1] : '';
                        
                        $csv .= sprintf('"%s","%s","%s","%s","%s"' . "\n",
                            date('Y-m-d', strtotime($timestamp)),
                            date('H:i:s', strtotime($timestamp)),
                            $logLevel,
                            str_replace('"', '""', $messageText),
                            str_replace('"', '""', $context)
                        );
                    }
                }
            }
            
            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
        }
        
        return $csv;
    }

    /**
     * Set minimum log level
     */
    public function setMinLevel($level)
    {
        $this->minLevel = $level;
    }

    /**
     * Set maximum file size before rotation
     */
    public function setMaxFileSize($size)
    {
        $this->maxFileSize = $size;
    }

    /**
     * Set maximum number of log files to keep
     */
    public function setMaxFiles($count)
    {
        $this->maxFiles = $count;
    }
}
