<?php

namespace TelegramBot\Controllers;

use TelegramBot\TelegramAPI;
use TelegramBot\Services\FileService;
use TelegramBot\Services\UserService;
use TelegramBot\Services\ValidationService;

/**
 * Broadcast Controller Class
 * 
 * Handles broadcasting messages to all users with different formats
 * (text, markdown, HTML, forward) and message management.
 */
class BroadcastController
{
    private $api;
    private $fileService;
    private $userService;
    private $validationService;

    public function __construct(TelegramAPI $api, FileService $fileService, UserService $userService, ValidationService $validationService)
    {
        $this->api = $api;
        $this->fileService = $fileService;
        $this->userService = $userService;
        $this->validationService = $validationService;
    }

    /**
     * Show broadcast panel
     */
    public function showBroadcastPanel($chatId, $messageId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => 'إذاعة توجيه', 'callback_data' => 'broadcast_forward']],
            [
                ['text' => 'إذاعة HTML', 'callback_data' => 'broadcast_html'],
                ['text' => 'إذاعة ماركداون', 'callback_data' => 'broadcast_markdown']
            ],
            [['text' => 'إذاعة نص عادي', 'callback_data' => 'broadcast_text']],
            [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
        ]);

        $text = "📢 مرحبا بك في قسم الإذاعة 🔥\n\nاختر نوع الإذاعة المطلوب:";

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Start broadcast process
     */
    public function startBroadcast($chatId, $messageId, $type)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• إلغاء •', 'callback_data' => 'broadcast_cancel']]
        ]);

        $messages = [
            'forward' => 'أرسل الآن الرسالة التي تريد توجيهها لجميع المستخدمين',
            'html' => 'أرسل الآن النص بتنسيق HTML',
            'markdown' => 'أرسل الآن النص بتنسيق Markdown',
            'text' => 'أرسل الآن النص العادي'
        ];

        $text = $messages[$type] ?? 'أرسل الرسالة الآن';

        // Set broadcast state
        $this->fileService->writeFile(ADMIN_PATH . 'broadcast_state.txt', $type);
        $this->fileService->writeFile(ADMIN_PATH . 'broadcast_user.txt', $chatId);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Process broadcast message
     */
    public function processBroadcast($message, $type)
    {
        $chatId = $message['chat']['id'];
        $messageId = $message['message_id'];

        // Validate message
        if ($type !== 'forward') {
            $validation = $this->validationService->validateBroadcastMessage($message['text'] ?? '');
            if (!$validation['valid']) {
                $this->api->sendMessage($chatId, "❌ " . $validation['error']);
                return false;
            }
        }

        // Get all members
        $members = $this->fileService->getMembers();
        $members = array_filter($members);

        if (empty($members)) {
            $this->api->sendMessage($chatId, "❌ لا يوجد مستخدمين للإذاعة إليهم");
            return false;
        }

        // Start broadcasting
        $this->api->sendMessage($chatId, "🔄 جاري الإذاعة...");

        $successCount = 0;
        $failCount = 0;
        $broadcastId = time();
        $sentMessages = [];

        foreach ($members as $memberId) {
            if (empty($memberId) || $this->fileService->isBanned($memberId)) {
                continue;
            }

            $result = $this->sendBroadcastMessage($memberId, $message, $type);
            
            if ($result && $result['ok']) {
                $successCount++;
                $sentMessageId = $result['result']['message_id'];
                $sentMessages[] = "$memberId:$sentMessageId";
            } else {
                $failCount++;
            }

            // Small delay to avoid rate limiting
            usleep(50000); // 50ms delay
        }

        // Store broadcast info for deletion
        $this->fileService->writeFile(
            STORAGE_PATH . "broadcasts/$broadcastId.txt",
            implode("\n", $sentMessages)
        );

        // Update statistics
        $this->fileService->incrementStatistic('total_broadcasts');

        // Send completion message
        $completionText = "✅ تم الإذاعة بنجاح!\n\n";
        $completionText .= "📊 الإحصائيات:\n";
        $completionText .= "✅ نجح: $successCount\n";
        $completionText .= "❌ فشل: $failCount\n";
        $completionText .= "📝 الإجمالي: " . ($successCount + $failCount);

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '🗑️ حذف الرسالة', 'callback_data' => "broadcast_delete_$broadcastId"]],
            [['text' => '• رجوع •', 'callback_data' => 'broadcast_panel']]
        ]);

        $this->api->sendMessage($chatId, $completionText, [
            'reply_markup' => $keyboard
        ]);

        // Clear broadcast state
        $this->clearBroadcastState();

        // Log broadcast
        $this->fileService->logMessage('info', "Broadcast completed", [
            'type' => $type,
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'broadcast_id' => $broadcastId
        ]);

        return true;
    }

    /**
     * Send broadcast message to individual user
     */
    private function sendBroadcastMessage($userId, $message, $type)
    {
        switch ($type) {
            case 'forward':
                return $this->api->forwardMessage(
                    $userId,
                    $message['chat']['id'],
                    $message['message_id']
                );

            case 'html':
                return $this->api->sendMessage($userId, $message['text'], [
                    'parse_mode' => 'HTML',
                    'disable_web_page_preview' => true
                ]);

            case 'markdown':
                return $this->api->sendMessage($userId, $message['text'], [
                    'parse_mode' => 'Markdown',
                    'disable_web_page_preview' => true
                ]);

            case 'text':
                return $this->api->sendMessage($userId, $message['text'], [
                    'disable_web_page_preview' => true
                ]);

            default:
                return false;
        }
    }

    /**
     * Delete broadcast messages
     */
    public function deleteBroadcast($chatId, $messageId, $broadcastId)
    {
        $broadcastFile = STORAGE_PATH . "broadcasts/$broadcastId.txt";
        
        if (!file_exists($broadcastFile)) {
            return $this->api->editMessageText($chatId, $messageId, "❌ لم يتم العثور على الإذاعة");
        }

        $this->api->editMessageText($chatId, $messageId, "🔄 جاري حذف الرسائل...");

        $sentMessages = $this->fileService->readLines($broadcastFile);
        $deletedCount = 0;

        foreach ($sentMessages as $messageInfo) {
            if (empty($messageInfo)) continue;
            
            $parts = explode(':', $messageInfo);
            if (count($parts) !== 2) continue;
            
            $userId = $parts[0];
            $messageId = $parts[1];

            $result = $this->api->deleteMessage($userId, $messageId);
            if ($result && $result['ok']) {
                $deletedCount++;
            }

            // Small delay
            usleep(30000); // 30ms delay
        }

        // Delete broadcast file
        $this->fileService->deleteFile($broadcastFile);

        $text = "✅ تم حذف $deletedCount رسالة بنجاح";
        
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'broadcast_panel']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Cancel broadcast
     */
    public function cancelBroadcast($chatId, $messageId)
    {
        $this->clearBroadcastState();

        $text = "❌ تم إلغاء الإذاعة";
        
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'broadcast_panel']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Clear broadcast state
     */
    private function clearBroadcastState()
    {
        $this->fileService->deleteFile(ADMIN_PATH . 'broadcast_state.txt');
        $this->fileService->deleteFile(ADMIN_PATH . 'broadcast_user.txt');
    }

    /**
     * Get broadcast state
     */
    public function getBroadcastState()
    {
        $state = $this->fileService->readFile(ADMIN_PATH . 'broadcast_state.txt');
        $user = $this->fileService->readFile(ADMIN_PATH . 'broadcast_user.txt');
        
        return [
            'type' => trim($state),
            'user' => trim($user)
        ];
    }

    /**
     * Check if user is in broadcast mode
     */
    public function isInBroadcastMode($userId)
    {
        $state = $this->getBroadcastState();
        return !empty($state['type']) && $state['user'] == $userId;
    }

    /**
     * Get broadcast statistics
     */
    public function getBroadcastStatistics()
    {
        $broadcastFiles = glob(STORAGE_PATH . 'broadcasts/*.txt');
        $totalBroadcasts = count($broadcastFiles);
        
        $stats = $this->fileService->getStatistics();
        $totalBroadcastsSent = $stats['total_broadcasts'] ?? 0;

        return [
            'total_broadcasts' => $totalBroadcastsSent,
            'active_broadcasts' => $totalBroadcasts
        ];
    }

    /**
     * Clean old broadcast files
     */
    public function cleanOldBroadcasts($days = 7)
    {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $broadcastFiles = glob(STORAGE_PATH . 'broadcasts/*.txt');
        
        foreach ($broadcastFiles as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
            }
        }
    }

    /**
     * Schedule broadcast (for future implementation)
     */
    public function scheduleBroadcast($message, $type, $scheduleTime)
    {
        $scheduleData = [
            'message' => $message,
            'type' => $type,
            'schedule_time' => $scheduleTime,
            'created_at' => time()
        ];

        $scheduleFile = STORAGE_PATH . 'scheduled_broadcasts/' . time() . '.json';
        $this->fileService->createDirectory(dirname($scheduleFile));
        
        return $this->fileService->writeJson($scheduleFile, $scheduleData);
    }

    /**
     * Process scheduled broadcasts (for future implementation)
     */
    public function processScheduledBroadcasts()
    {
        $scheduledFiles = glob(STORAGE_PATH . 'scheduled_broadcasts/*.json');
        $currentTime = time();

        foreach ($scheduledFiles as $file) {
            $data = $this->fileService->readJson($file);
            
            if ($data['schedule_time'] <= $currentTime) {
                // Process the broadcast
                $this->processBroadcast($data['message'], $data['type']);
                
                // Remove the scheduled file
                unlink($file);
            }
        }
    }
}
