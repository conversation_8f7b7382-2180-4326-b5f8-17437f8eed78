<?php

namespace TelegramBot;

/**
 * Telegram API Wrapper Class
 * 
 * This class handles all communication with the Telegram Bot API
 * and provides a clean interface for sending messages and handling responses.
 */
class TelegramAPI
{
    private $token;
    private $apiUrl;
    private $lastError;

    public function __construct($token = null)
    {
        $this->token = $token ?: BOT_TOKEN;
        $this->apiUrl = 'https://api.telegram.org/bot' . $this->token . '/';
    }

    /**
     * Make a request to the Telegram API
     */
    public function makeRequest($method, $data = [])
    {
        $url = $this->apiUrl . $method;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'TelegramBot/2.0'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_error($ch)) {
            $this->lastError = 'cURL Error: ' . curl_error($ch);
            curl_close($ch);
            return false;
        }
        
        curl_close($ch);

        if ($httpCode !== 200) {
            $this->lastError = "HTTP Error: $httpCode";
            return false;
        }

        $decoded = json_decode($response, true);
        
        if (!$decoded || !$decoded['ok']) {
            $this->lastError = $decoded['description'] ?? 'Unknown API error';
            return false;
        }

        return $decoded;
    }

    /**
     * Send a text message
     */
    public function sendMessage($chatId, $text, $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => 'Markdown',
            'disable_web_page_preview' => true
        ], $options);

        return $this->makeRequest('sendMessage', $data);
    }

    /**
     * Send a photo
     */
    public function sendPhoto($chatId, $photo, $caption = '', $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'photo' => $photo,
            'caption' => $caption,
            'parse_mode' => 'Markdown'
        ], $options);

        return $this->makeRequest('sendPhoto', $data);
    }

    /**
     * Forward a message
     */
    public function forwardMessage($chatId, $fromChatId, $messageId, $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'from_chat_id' => $fromChatId,
            'message_id' => $messageId
        ], $options);

        return $this->makeRequest('forwardMessage', $data);
    }

    /**
     * Edit message text
     */
    public function editMessageText($chatId, $messageId, $text, $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'message_id' => $messageId,
            'text' => $text,
            'parse_mode' => 'Markdown'
        ], $options);

        return $this->makeRequest('editMessageText', $data);
    }

    /**
     * Delete a message
     */
    public function deleteMessage($chatId, $messageId)
    {
        return $this->makeRequest('deleteMessage', [
            'chat_id' => $chatId,
            'message_id' => $messageId
        ]);
    }

    /**
     * Answer callback query
     */
    public function answerCallbackQuery($callbackQueryId, $text = '', $showAlert = false)
    {
        return $this->makeRequest('answerCallbackQuery', [
            'callback_query_id' => $callbackQueryId,
            'text' => $text,
            'show_alert' => $showAlert
        ]);
    }

    /**
     * Get chat member information
     */
    public function getChatMember($chatId, $userId)
    {
        return $this->makeRequest('getChatMember', [
            'chat_id' => $chatId,
            'user_id' => $userId
        ]);
    }

    /**
     * Get chat information
     */
    public function getChat($chatId)
    {
        return $this->makeRequest('getChat', [
            'chat_id' => $chatId
        ]);
    }

    /**
     * Set webhook
     */
    public function setWebhook($url, $options = [])
    {
        $data = array_merge([
            'url' => $url
        ], $options);

        return $this->makeRequest('setWebhook', $data);
    }

    /**
     * Delete webhook
     */
    public function deleteWebhook()
    {
        return $this->makeRequest('deleteWebhook');
    }

    /**
     * Get webhook info
     */
    public function getWebhookInfo()
    {
        return $this->makeRequest('getWebhookInfo');
    }

    /**
     * Send document
     */
    public function sendDocument($chatId, $document, $caption = '', $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'document' => $document,
            'caption' => $caption
        ], $options);

        return $this->makeRequest('sendDocument', $data);
    }

    /**
     * Send video
     */
    public function sendVideo($chatId, $video, $caption = '', $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'video' => $video,
            'caption' => $caption
        ], $options);

        return $this->makeRequest('sendVideo', $data);
    }

    /**
     * Send audio
     */
    public function sendAudio($chatId, $audio, $caption = '', $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'audio' => $audio,
            'caption' => $caption
        ], $options);

        return $this->makeRequest('sendAudio', $data);
    }

    /**
     * Send voice
     */
    public function sendVoice($chatId, $voice, $caption = '', $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'voice' => $voice,
            'caption' => $caption
        ], $options);

        return $this->makeRequest('sendVoice', $data);
    }

    /**
     * Send sticker
     */
    public function sendSticker($chatId, $sticker, $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'sticker' => $sticker
        ], $options);

        return $this->makeRequest('sendSticker', $data);
    }

    /**
     * Get last error message
     */
    public function getLastError()
    {
        return $this->lastError;
    }

    /**
     * Get API URL
     */
    public function getApiUrl()
    {
        return $this->apiUrl;
    }

    /**
     * Create inline keyboard markup
     */
    public static function createInlineKeyboard($buttons)
    {
        return json_encode(['inline_keyboard' => $buttons]);
    }

    /**
     * Create reply keyboard markup
     */
    public static function createReplyKeyboard($buttons, $options = [])
    {
        $keyboard = array_merge([
            'keyboard' => $buttons,
            'resize_keyboard' => true,
            'one_time_keyboard' => false
        ], $options);

        return json_encode($keyboard);
    }

    /**
     * Remove keyboard
     */
    public static function removeKeyboard()
    {
        return json_encode(['remove_keyboard' => true]);
    }
}
