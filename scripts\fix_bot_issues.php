<?php
/**
 * Comprehensive Bot Issues Fix Script
 * 
 * This script fixes the three main issues:
 * 1. Missing Admin Panel
 * 2. Cannot Reply to Messages  
 * 3. Message Delivery Issues (duplicates/delays)
 */

echo "🔧 VEVoGamez Bot Issues Fix\n";
echo "===========================\n\n";

// Load configuration and classes
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/TelegramAPI.php';
require_once __DIR__ . '/../src/Services/FileService.php';
require_once __DIR__ . '/../src/Services/UserService.php';

$config = require __DIR__ . '/../config/config.php';
$api = new TelegramBot\TelegramAPI();
$fileService = new TelegramBot\Services\FileService($config);
$userService = new TelegramBot\Services\UserService($fileService, $api);

echo "📋 Current Configuration:\n";
echo "- Main Admin ID: " . MAIN_ADMIN_ID . "\n";
echo "- Bot Token: " . (BOT_TOKEN ? "✅ Set" : "❌ Missing") . "\n";
echo "- Storage Path: " . STORAGE_PATH . "\n\n";

// Fix 1: Admin Panel Issue
echo "🔧 Fix 1: Admin Panel Issue\n";
echo "============================\n";

// Ensure admin file exists and contains the main admin
$adminFile = $config['files']['admins'];
echo "Checking admin file: $adminFile\n";

if (!file_exists($adminFile)) {
    echo "❌ Admin file missing, creating...\n";
    file_put_contents($adminFile, MAIN_ADMIN_ID . "\n");
    echo "✅ Admin file created\n";
} else {
    $admins = file_get_contents($adminFile);
    $adminList = array_filter(explode("\n", trim($admins)));
    
    if (!in_array(MAIN_ADMIN_ID, $adminList)) {
        echo "❌ Main admin ID not in admin file, adding...\n";
        file_put_contents($adminFile, MAIN_ADMIN_ID . "\n", FILE_APPEND);
        echo "✅ Main admin ID added\n";
    } else {
        echo "✅ Main admin ID found in admin file\n";
    }
}

// Test admin functions
$isAdmin = $userService->isAdmin(MAIN_ADMIN_ID);
$isMainAdmin = $userService->isMainAdmin(MAIN_ADMIN_ID);
echo "Admin check results:\n";
echo "- Is admin: " . ($isAdmin ? "✅ Yes" : "❌ No") . "\n";
echo "- Is main admin: " . ($isMainAdmin ? "✅ Yes" : "❌ No") . "\n\n";

// Fix 2: Message Reply System
echo "🔧 Fix 2: Message Reply System\n";
echo "===============================\n";

// Ensure messages directory exists
$messagesPath = MESSAGES_PATH;
if (!is_dir($messagesPath)) {
    echo "❌ Messages directory missing, creating...\n";
    mkdir($messagesPath, 0755, true);
    echo "✅ Messages directory created\n";
} else {
    echo "✅ Messages directory exists\n";
}

// Check message mappings
$mappingFiles = glob($messagesPath . '*.txt');
echo "Current message mappings: " . count($mappingFiles) . "\n";

// Clean old mappings (older than 7 days)
$cutoffTime = time() - (7 * 24 * 60 * 60);
$cleanedCount = 0;
foreach ($mappingFiles as $file) {
    if (filemtime($file) < $cutoffTime) {
        unlink($file);
        $cleanedCount++;
    }
}
if ($cleanedCount > 0) {
    echo "🧹 Cleaned $cleanedCount old message mappings\n";
}

// Test MessageService
echo "Testing MessageService...\n";
try {
    require_once __DIR__ . '/../src/Services/MessageService.php';
    $messageService = new TelegramBot\Services\MessageService($fileService, $api, $userService);
    echo "✅ MessageService created successfully\n";
} catch (Exception $e) {
    echo "❌ MessageService error: " . $e->getMessage() . "\n";
}

echo "\n";

// Fix 3: Message Delivery Issues
echo "🔧 Fix 3: Message Delivery Issues\n";
echo "==================================\n";

// Create rate limiting directory
$rateLimitPath = STORAGE_PATH . 'rate_limit/';
if (!is_dir($rateLimitPath)) {
    echo "❌ Rate limit directory missing, creating...\n";
    mkdir($rateLimitPath, 0755, true);
    echo "✅ Rate limit directory created\n";
} else {
    echo "✅ Rate limit directory exists\n";
}

// Clean old rate limit files
$rateLimitFiles = glob($rateLimitPath . '*.txt');
$cleanedRateFiles = 0;
foreach ($rateLimitFiles as $file) {
    if (filemtime($file) < time() - 3600) { // Older than 1 hour
        unlink($file);
        $cleanedRateFiles++;
    }
}
if ($cleanedRateFiles > 0) {
    echo "🧹 Cleaned $cleanedRateFiles old rate limit files\n";
}

echo "Rate limiting settings:\n";
echo "- Messages per minute: " . RATE_LIMIT_MESSAGES . "\n";
echo "- Time window: " . RATE_LIMIT_TIME . " seconds\n";

// Check and fix settings file
$settingsFile = $config['files']['settings'];
echo "\nChecking settings file...\n";

if (!file_exists($settingsFile)) {
    echo "❌ Settings file missing, creating...\n";
    $defaultSettings = [
        'notifications_enabled' => true,
        'subscription_check' => true,
        'auto_reply_enabled' => true,
        'bot_enabled' => true,
        'media_restrictions' => [
            'photos' => false,
            'videos' => false,
            'documents' => false,
            'stickers' => false,
            'voice' => false,
            'audio' => false,
            'forwards' => false
        ]
    ];
    file_put_contents($settingsFile, json_encode($defaultSettings, JSON_PRETTY_PRINT));
    echo "✅ Settings file created\n";
} else {
    $settings = json_decode(file_get_contents($settingsFile), true);
    if (!$settings) {
        echo "❌ Settings file corrupted, recreating...\n";
        $defaultSettings = [
            'notifications_enabled' => true,
            'subscription_check' => true,
            'auto_reply_enabled' => true,
            'bot_enabled' => true,
            'media_restrictions' => [
                'photos' => false,
                'videos' => false,
                'documents' => false,
                'stickers' => false,
                'voice' => false,
                'audio' => false,
                'forwards' => false
            ]
        ];
        file_put_contents($settingsFile, json_encode($defaultSettings, JSON_PRETTY_PRINT));
        echo "✅ Settings file recreated\n";
    } else {
        echo "✅ Settings file is valid\n";
        echo "- Bot enabled: " . ($settings['bot_enabled'] ?? 'true') . "\n";
        echo "- Auto reply enabled: " . ($settings['auto_reply_enabled'] ?? 'true') . "\n";
    }
}

// Check statistics file
$statsFile = $config['files']['statistics'];
if (!file_exists($statsFile)) {
    echo "❌ Statistics file missing, creating...\n";
    $defaultStats = [
        'total_users' => 0,
        'total_messages_received' => 0,
        'total_messages_sent' => 0,
        'total_broadcasts' => 0,
        'bot_started' => date('Y-m-d H:i:s')
    ];
    file_put_contents($statsFile, json_encode($defaultStats, JSON_PRETTY_PRINT));
    echo "✅ Statistics file created\n";
} else {
    echo "✅ Statistics file exists\n";
}

echo "\n";

// Test Bot API Connection
echo "🔧 Bot API Connection Test\n";
echo "==========================\n";

try {
    $botInfo = $api->makeRequest('getMe');
    if ($botInfo && $botInfo['ok']) {
        echo "✅ Bot API connection successful\n";
        echo "Bot name: " . $botInfo['result']['first_name'] . "\n";
        echo "Bot username: @" . $botInfo['result']['username'] . "\n";
    } else {
        echo "❌ Bot API connection failed\n";
        if (isset($botInfo['description'])) {
            echo "Error: " . $botInfo['description'] . "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Bot API error: " . $e->getMessage() . "\n";
}

echo "\n";

// Create test message for admin
echo "🔧 Testing Admin Panel Access\n";
echo "=============================\n";

try {
    $testMessage = [
        'from' => ['id' => MAIN_ADMIN_ID, 'first_name' => 'Test Admin'],
        'chat' => ['id' => MAIN_ADMIN_ID, 'type' => 'private'],
        'text' => '/start',
        'message_id' => 1
    ];
    
    require_once __DIR__ . '/../src/Controllers/UserController.php';
    require_once __DIR__ . '/../src/Services/ValidationService.php';
    require_once __DIR__ . '/../src/Services/MessageService.php';
    
    $validationService = new TelegramBot\Services\ValidationService();
    $messageService = new TelegramBot\Services\MessageService($fileService, $api, $userService);
    $userController = new TelegramBot\Controllers\UserController($api, $fileService, $userService, $validationService, $messageService);
    
    echo "✅ Controllers created successfully\n";
    echo "✅ Admin panel should now work for user ID: " . MAIN_ADMIN_ID . "\n";
    
} catch (Exception $e) {
    echo "❌ Controller creation error: " . $e->getMessage() . "\n";
}

echo "\n";

// Final recommendations
echo "📋 Final Recommendations\n";
echo "========================\n";
echo "1. Test /start command with admin user ID: " . MAIN_ADMIN_ID . "\n";
echo "2. Send a test message from a regular user to test forwarding\n";
echo "3. Try replying to the forwarded message to test reply system\n";
echo "4. Check webhook status: /path/to/bot/public/setup.php\n";
echo "5. Monitor logs: " . LOGS_PATH . date('Y-m-d') . ".log\n\n";

echo "🎯 All fixes applied successfully!\n";
echo "The bot should now work correctly for:\n";
echo "✅ Admin panel access\n";
echo "✅ Message forwarding and replies\n";
echo "✅ Proper message delivery without duplicates\n\n";

echo "If issues persist, check:\n";
echo "- Webhook URL configuration\n";
echo "- Server permissions\n";
echo "- Bot token validity\n";
echo "- Network connectivity\n";
