<?php
/**
 * Example Configuration File
 *
 * Copy this file to config.php and update the values according to your setup.
 * This file contains all the configuration options for the Telegram bot.
 */

// Prevent multiple inclusions
if (defined('BOT_CONFIG_LOADED')) {
    return $config ?? [];
}
define('BOT_CONFIG_LOADED', true);

// Bot Configuration
if (!defined('BOT_TOKEN')) {
    define('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE'); // Get from @BotFather
}
if (!defined('API_URL')) {
    define('API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN . '/');
}

// Admin Configuration
if (!defined('MAIN_ADMIN_ID')) {
    define('MAIN_ADMIN_ID', 123456789); // Your Telegram user ID
}
if (!defined('MAX_ADMINS')) {
    define('MAX_ADMINS', 5); // Maximum number of admins allowed
}

// Channel Configuration
if (!defined('CHANNEL_USERNAME')) {
    define('CHANNEL_USERNAME', '@YourChannel'); // Your channel username
}
if (!defined('DEVELOPER_USERNAME')) {
    define('DEVELOPER_USERNAME', '@YourUsername'); // Developer username
}

// File Storage Paths
if (!defined('STORAGE_PATH')) {
    define('STORAGE_PATH', __DIR__ . '/../storage/');
}
if (!defined('USERS_PATH')) {
    define('USERS_PATH', STORAGE_PATH . 'users/');
}
if (!defined('MESSAGES_PATH')) {
    define('MESSAGES_PATH', STORAGE_PATH . 'messages/');
}
if (!defined('ADMIN_PATH')) {
    define('ADMIN_PATH', STORAGE_PATH . 'admin/');
}
if (!defined('LOGS_PATH')) {
    define('LOGS_PATH', STORAGE_PATH . 'logs/');
}

// Bot Settings
if (!defined('BOT_NAME')) {
    define('BOT_NAME', 'Your Bot Name');
}
if (!defined('BOT_VERSION')) {
    define('BOT_VERSION', '2.0.0');
}
if (!defined('BOT_DESCRIPTION')) {
    define('BOT_DESCRIPTION', 'Your bot description here');
}

// Security Settings
if (!defined('MAX_MESSAGE_LENGTH')) {
    define('MAX_MESSAGE_LENGTH', 4096);
}
if (!defined('RATE_LIMIT_MESSAGES')) {
    define('RATE_LIMIT_MESSAGES', 20); // Messages per minute
}
if (!defined('RATE_LIMIT_TIME')) {
    define('RATE_LIMIT_TIME', 60); // Time window in seconds
}

// Feature Flags
if (!defined('ENABLE_SUBSCRIPTION_CHECK')) {
    define('ENABLE_SUBSCRIPTION_CHECK', true);
}
if (!defined('ENABLE_ADMIN_NOTIFICATIONS')) {
    define('ENABLE_ADMIN_NOTIFICATIONS', true);
}
if (!defined('ENABLE_MESSAGE_LOGGING')) {
    define('ENABLE_MESSAGE_LOGGING', true);
}
if (!defined('ENABLE_STATISTICS')) {
    define('ENABLE_STATISTICS', true);
}

// Default Messages
if (!defined('WELCOME_MESSAGE')) {
    define('WELCOME_MESSAGE', "Welcome to our bot! 🤖\n\nYou can send us messages and we'll respond as soon as possible.");
}
if (!defined('AUTO_REPLY_MESSAGE')) {
    define('AUTO_REPLY_MESSAGE', 'Thank you for your message! We will reply soon.');
}
if (!defined('BANNED_MESSAGE')) {
    define('BANNED_MESSAGE', '❌ Sorry, you are banned from using this bot.');
}
if (!defined('SUBSCRIPTION_REQUIRED_MESSAGE')) {
    define('SUBSCRIPTION_REQUIRED_MESSAGE', "Please subscribe to our channel first:\n\n@YourChannel\n\nThen send /start again.");
}

// Error Messages
if (!defined('ERROR_GENERAL')) {
    define('ERROR_GENERAL', 'An error occurred, please try again');
}
if (!defined('ERROR_PERMISSION_DENIED')) {
    define('ERROR_PERMISSION_DENIED', 'You do not have permission to perform this action');
}
if (!defined('ERROR_USER_NOT_FOUND')) {
    define('ERROR_USER_NOT_FOUND', 'User not found');
}
if (!defined('ERROR_INVALID_INPUT')) {
    define('ERROR_INVALID_INPUT', 'Invalid input provided');
}

// Success Messages
if (!defined('SUCCESS_ADMIN_ADDED')) {
    define('SUCCESS_ADMIN_ADDED', 'User promoted to admin successfully ✅');
}
if (!defined('SUCCESS_ADMIN_REMOVED')) {
    define('SUCCESS_ADMIN_REMOVED', 'User removed from admin successfully ✅');
}
if (!defined('SUCCESS_USER_BANNED')) {
    define('SUCCESS_USER_BANNED', 'User banned successfully ✅');
}
if (!defined('SUCCESS_USER_UNBANNED')) {
    define('SUCCESS_USER_UNBANNED', 'User unbanned successfully ✅');
}
if (!defined('SUCCESS_MESSAGE_SENT')) {
    define('SUCCESS_MESSAGE_SENT', 'Message sent successfully ✅');
}
if (!defined('SUCCESS_BROADCAST_SENT')) {
    define('SUCCESS_BROADCAST_SENT', 'Broadcast sent successfully 🎉');
}

// Database/File Configuration
$config = [
    'files' => [
        'members' => USERS_PATH . 'members.txt',
        'admins' => ADMIN_PATH . 'admins.txt',
        'banned' => USERS_PATH . 'banned.txt',
        'channels' => ADMIN_PATH . 'channels.txt',
        'settings' => ADMIN_PATH . 'settings.json',
        'statistics' => ADMIN_PATH . 'statistics.json',
        'welcome_message' => ADMIN_PATH . 'welcome.txt',
        'auto_reply' => ADMIN_PATH . 'auto_reply.txt'
    ],
    'directories' => [
        'storage' => STORAGE_PATH,
        'users' => USERS_PATH,
        'messages' => MESSAGES_PATH,
        'admin' => ADMIN_PATH,
        'logs' => LOGS_PATH,
        'temp' => STORAGE_PATH . 'temp/',
        'backups' => STORAGE_PATH . 'backups/'
    ]
];

// Initialize directories
foreach ($config['directories'] as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Initialize files with default content if they don't exist
$defaultFiles = [
    $config['files']['members'] => '',
    $config['files']['admins'] => MAIN_ADMIN_ID . "\n",
    $config['files']['banned'] => '',
    $config['files']['channels'] => '',
    $config['files']['welcome_message'] => WELCOME_MESSAGE,
    $config['files']['auto_reply'] => AUTO_REPLY_MESSAGE,
    $config['files']['settings'] => json_encode([
        'notifications_enabled' => true,
        'subscription_check' => true,
        'auto_reply_enabled' => true,
        'bot_enabled' => true,
        'media_restrictions' => [
            'photos' => false,
            'videos' => false,
            'documents' => false,
            'stickers' => false,
            'voice' => false,
            'audio' => false,
            'forwards' => false
        ]
    ], JSON_PRETTY_PRINT),
    $config['files']['statistics'] => json_encode([
        'total_users' => 0,
        'total_messages_received' => 0,
        'total_messages_sent' => 0,
        'total_broadcasts' => 0,
        'bot_started' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT)
];

foreach ($defaultFiles as $file => $content) {
    if (!file_exists($file)) {
        file_put_contents($file, $content);
    }
}

// Environment-specific settings
if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true) {
    // Development settings
    if (!defined('DEBUG_MODE')) {
        define('DEBUG_MODE', true);
    }
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    // Use different storage path for development
    // define('STORAGE_PATH', __DIR__ . '/../storage_dev/');
} else {
    // Production settings
    if (!defined('DEBUG_MODE')) {
        define('DEBUG_MODE', false);
    }
    error_reporting(E_ERROR | E_PARSE);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', LOGS_PATH . 'php_errors.log');
}

// Optional: Database configuration (if you want to use database instead of files)
/*
$config['database'] = [
    'type' => 'mysql', // mysql, sqlite, postgresql
    'host' => 'localhost',
    'port' => 3306,
    'database' => 'telegram_bot',
    'username' => 'bot_user',
    'password' => 'secure_password',
    'charset' => 'utf8mb4'
];
*/

// Optional: External API configurations
/*
$config['external_apis'] = [
    'weather_api_key' => 'your_weather_api_key',
    'news_api_key' => 'your_news_api_key',
    'translate_api_key' => 'your_translate_api_key'
];
*/

// Optional: Webhook configuration
/*
$config['webhook'] = [
    'url' => 'https://yourdomain.com/bot/public/webhook.php',
    'certificate' => '/path/to/certificate.pem', // For self-signed certificates
    'max_connections' => 40,
    'allowed_updates' => ['message', 'callback_query']
];
*/

// Optional: Logging configuration
/*
$config['logging'] = [
    'level' => 'info', // debug, info, warning, error, critical
    'max_file_size' => 10 * 1024 * 1024, // 10MB
    'max_files' => 30,
    'separate_error_log' => true
];
*/

// Optional: Security configuration
/*
$config['security'] = [
    'rate_limit' => [
        'enabled' => true,
        'max_requests' => 20,
        'time_window' => 60
    ],
    'blocked_words' => [
        'spam',
        'scam'
    ],
    'blocked_domains' => [
        'malicious-site.com'
    ],
    'max_file_size' => 50 * 1024 * 1024 // 50MB
];
*/

return $config;
