# VEVoGamez Telegram Bot

A professional, modular Telegram bot built with PHP for managing user interactions, broadcasting messages, and providing games/apps recommendations. This bot features a complete admin panel, subscription management, and comprehensive security features.

## 🚀 Features

### Core Features
- **Modular Architecture**: Clean, maintainable code structure with separation of concerns
- **Admin Panel**: Complete administrative interface with inline keyboards
- **User Management**: Ban/unban users, promote/demote admins
- **Broadcasting System**: Send messages to all users (text, HTML, Markdown, forward)
- **Subscription Management**: Force users to subscribe to channels before using the bot
- **Message Forwarding**: Forward user messages to admins and reply back
- **Games & Apps Menu**: Organized catalog of games and applications
- **Statistics**: Comprehensive usage statistics and analytics

### Security Features
- **Input Validation**: Comprehensive validation and sanitization
- **Rate Limiting**: Prevent spam and abuse
- **Authentication**: Multi-level permission system
- **Security Middleware**: Detect and prevent malicious activities
- **Logging**: Detailed logging for monitoring and debugging

### Technical Features
- **PSR-12 Compliant**: Follows PHP coding standards
- **Error Handling**: Robust error handling and logging
- **File-based Storage**: No database required, uses efficient file storage
- **Webhook Support**: Optimized for webhook-based operation
- **Easy Deployment**: Simple setup and configuration

## 📋 Requirements

- PHP 7.4 or higher
- cURL extension enabled
- Write permissions for storage directories
- HTTPS-enabled web server (for webhook)
- Telegram Bot Token

## 🛠️ Installation

### 1. Download and Extract

```bash
# Clone or download the project
git clone https://github.com/yourusername/vevogamez-bot.git
cd vevogamez-bot
```

### 2. Configure the Bot

1. **Get Bot Token**: Create a new bot with [@BotFather](https://t.me/BotFather) on Telegram
2. **Edit Configuration**: Update `config/config.php` with your settings:

```php
// Bot Configuration
define('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE');
define('MAIN_ADMIN_ID', YOUR_TELEGRAM_USER_ID);
define('CHANNEL_USERNAME', '@YourChannel');
```

### 3. Set Permissions

```bash
# Make storage directories writable
chmod -R 755 storage/
chmod -R 755 config/
```

### 4. Setup Webhook

1. Upload files to your web server
2. Access `public/setup.php` in your browser
3. Click "Set Webhook" to configure the webhook URL

## 📁 Project Structure

```
VEVoGamez-Bot/
├── config/
│   └── config.php              # Main configuration file
├── src/
│   ├── Controllers/            # Request handlers
│   │   ├── AdminController.php
│   │   ├── BroadcastController.php
│   │   ├── GameController.php
│   │   └── UserController.php
│   ├── Middleware/             # Request middleware
│   │   ├── AuthMiddleware.php
│   │   ├── SecurityMiddleware.php
│   │   └── SubscriptionMiddleware.php
│   ├── Services/               # Business logic
│   │   ├── FileService.php
│   │   ├── MessageService.php
│   │   ├── UserService.php
│   │   └── ValidationService.php
│   ├── Bot.php                 # Main bot class
│   ├── Router.php              # Request routing
│   └── TelegramAPI.php         # Telegram API wrapper
├── public/
│   ├── webhook.php             # Webhook endpoint
│   └── setup.php               # Setup interface
├── storage/                    # Data storage
│   ├── admin/                  # Admin configuration
│   ├── logs/                   # Log files
│   ├── messages/               # Message mappings
│   └── users/                  # User data
├── docs/                       # Documentation
└── README.md
```

## 🔧 Configuration

### Basic Configuration

Edit `config/config.php` to customize your bot:

```php
// Bot Settings
define('BOT_TOKEN', 'your_bot_token');
define('MAIN_ADMIN_ID', your_user_id);
define('CHANNEL_USERNAME', '@your_channel');

// Feature Flags
define('ENABLE_SUBSCRIPTION_CHECK', true);
define('ENABLE_ADMIN_NOTIFICATIONS', true);
define('ENABLE_MESSAGE_LOGGING', true);

// Security Settings
define('MAX_MESSAGE_LENGTH', 4096);
define('RATE_LIMIT_MESSAGES', 20);
define('RATE_LIMIT_TIME', 60);
```

### Advanced Configuration

For advanced settings, modify the configuration arrays in `config/config.php`:

```php
$config = [
    'files' => [
        'members' => USERS_PATH . 'members.txt',
        'admins' => ADMIN_PATH . 'admins.txt',
        // ... other file paths
    ],
    'directories' => [
        'storage' => STORAGE_PATH,
        'users' => USERS_PATH,
        // ... other directories
    ]
];
```

## 🎮 Usage

### Admin Commands

- **Admin Panel**: Send `/start` as an admin to access the admin panel
- **Promote User**: Send `ترقية` to generate a promotion code
- **Remove Admin**: Send `تنزيل [user_id]` to remove an admin
- **View Admins**: Send `الادمنية` to see the admin list

### User Commands

- **Start Bot**: `/start` - Initialize the bot and show welcome message
- **Request Promotion**: `ارفعني` - Request admin promotion
- **Games/Apps**: Send app names like `واتساب`, `تيليجرام`, etc.

### Admin Panel Features

Access the admin panel by sending `/start` as an admin:

1. **Bot Status**: Enable/disable bot functionality
2. **Subscription Management**: Add/remove required channels
3. **Broadcasting**: Send messages to all users
4. **Statistics**: View bot usage statistics
5. **Admin Management**: Promote/demote administrators
6. **Media Protection**: Control what media types are allowed

## 🔒 Security

### Built-in Security Features

- **Input Validation**: All user inputs are validated and sanitized
- **Rate Limiting**: Prevents spam and abuse
- **Permission System**: Multi-level access control
- **Malicious Content Detection**: Blocks suspicious patterns and links
- **File Type Validation**: Restricts dangerous file types
- **Session Management**: Secure session handling

### Security Best Practices

1. **Keep Bot Token Secret**: Never expose your bot token
2. **Use HTTPS**: Always use HTTPS for webhook endpoints
3. **Regular Updates**: Keep the bot code updated
4. **Monitor Logs**: Regularly check security logs
5. **Backup Data**: Regular backups of storage directory

## 📊 Monitoring

### Log Files

The bot generates detailed logs in the `storage/logs/` directory:

- **Daily Logs**: `YYYY-MM-DD.log` - General bot activity
- **Security Logs**: Security events and threats
- **Error Logs**: PHP errors and exceptions

### Statistics

Access statistics through:
- Admin panel in the bot
- `public/setup.php` web interface
- Log file analysis

## 🚀 Deployment

### Production Deployment

1. **Server Requirements**:
   - PHP 7.4+ with cURL
   - HTTPS-enabled web server
   - Sufficient disk space for logs and storage

2. **Security Hardening**:
   ```bash
   # Set proper permissions
   chmod 644 public/*.php
   chmod 755 storage/
   chmod 600 config/config.php
   ```

3. **Performance Optimization**:
   - Enable PHP OPcache
   - Configure proper error reporting
   - Set up log rotation

### Webhook Setup

1. Access `public/setup.php`
2. Verify bot information
3. Set webhook URL
4. Test bot functionality

## 🔧 Maintenance

### Regular Maintenance Tasks

The bot includes automatic maintenance features:

```php
// Run maintenance (can be automated via cron)
$bot = new TelegramBot\Bot();
$bot->performMaintenance();
```

Maintenance tasks include:
- Cleaning old log files
- Removing expired message mappings
- Cleaning old broadcast files
- Removing expired rate limit data

### Manual Maintenance

Access `public/setup.php` and click "Run Maintenance" to perform manual cleanup.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- **Developer**: [@GoogleYooz](https://t.me/GoogleYooz)
- **Channel**: [@VEVoGamez](https://t.me/VEVoGamez)
- **Issues**: Create an issue on GitHub

## 🔄 Migration from Old Version

If you're migrating from the old monolithic version, see [MIGRATION.md](docs/MIGRATION.md) for detailed instructions.

## 📚 Documentation

- [API Documentation](docs/API.md)
- [Configuration Guide](docs/CONFIGURATION.md)
- [Deployment Guide](docs/DEPLOYMENT.md)
- [Security Guide](docs/SECURITY.md)
- [Migration Guide](docs/MIGRATION.md)

---

**Made with ❤️ by VEVoGamez Team**
