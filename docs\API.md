# API Documentation

This document provides detailed information about the classes, methods, and APIs used in the VEVoGamez Telegram Bot.

## Table of Contents

- [Core Classes](#core-classes)
- [Services](#services)
- [Controllers](#controllers)
- [Middleware](#middleware)
- [Configuration](#configuration)

## Core Classes

### Bot Class

The main entry point for the bot application.

```php
namespace TelegramBot;

class Bot
{
    public function __construct()
    public function handleWebhook(): bool
    public function setWebhook(string $url, array $options = []): bool
    public function deleteWebhook(): bool
    public function getWebhookInfo(): array|false
    public function sendTestMessage(int $chatId = null): bool
    public function getBotInfo(): array|false
    public function performMaintenance(): bool
    public function getStatistics(): array|false
}
```

#### Methods

**`handleWebhook()`**
- Processes incoming webhook requests from Telegram
- Returns: `bool` - Success status
- Throws: `Exception` on processing errors

**`setWebhook(string $url, array $options = [])`**
- Sets the webhook URL for the bot
- Parameters:
  - `$url`: The webhook URL
  - `$options`: Additional webhook options
- Returns: `bool` - Success status

**`performMaintenance()`**
- Performs routine maintenance tasks
- Cleans old logs, message mappings, and temporary files
- Returns: `bool` - Success status

### TelegramAPI Class

Wrapper for Telegram Bot API interactions.

```php
namespace TelegramBot;

class TelegramAPI
{
    public function __construct(string $token = null)
    public function makeRequest(string $method, array $data = []): array|false
    public function sendMessage(int $chatId, string $text, array $options = []): array|false
    public function sendPhoto(int $chatId, string $photo, string $caption = '', array $options = []): array|false
    public function forwardMessage(int $chatId, int $fromChatId, int $messageId, array $options = []): array|false
    public function editMessageText(int $chatId, int $messageId, string $text, array $options = []): array|false
    public function deleteMessage(int $chatId, int $messageId): array|false
    public function answerCallbackQuery(string $callbackQueryId, string $text = '', bool $showAlert = false): array|false
    public function getChatMember(string $chatId, int $userId): array|false
    public function getChat(string $chatId): array|false
    public static function createInlineKeyboard(array $buttons): string
    public static function createReplyKeyboard(array $buttons, array $options = []): string
    public static function removeKeyboard(): string
    public function getLastError(): string
}
```

#### Static Methods

**`createInlineKeyboard(array $buttons)`**
- Creates inline keyboard markup
- Parameters:
  - `$buttons`: 2D array of button definitions
- Returns: `string` - JSON encoded keyboard markup

Example:
```php
$keyboard = TelegramAPI::createInlineKeyboard([
    [
        ['text' => 'Button 1', 'callback_data' => 'btn1'],
        ['text' => 'Button 2', 'callback_data' => 'btn2']
    ],
    [['text' => 'URL Button', 'url' => 'https://example.com']]
]);
```

### Router Class

Routes incoming updates to appropriate controllers.

```php
namespace TelegramBot;

class Router
{
    public function __construct(TelegramAPI $api, array $config)
    public function route(array $update): bool
}
```

## Services

### FileService Class

Handles all file operations and data persistence.

```php
namespace TelegramBot\Services;

class FileService
{
    public function __construct(array $config)
    
    // Basic file operations
    public function readFile(string $filename): string
    public function writeFile(string $filename, string $data, bool $append = false): int|false
    public function readLines(string $filename): array
    public function writeLines(string $filename, array $lines): int|false
    public function appendLine(string $filename, string $line): int|false
    public function removeLine(string $filename, string $line): int|false
    public function lineExists(string $filename, string $line): bool
    
    // JSON operations
    public function readJson(string $filename): array
    public function writeJson(string $filename, array $data): int|false
    public function updateJson(string $filename, string $key, mixed $value): int|false
    
    // User management
    public function getMembers(): array
    public function addMember(int $userId): bool
    public function getAdmins(): array
    public function addAdmin(int $userId): bool
    public function removeAdmin(int $userId): bool
    public function isAdmin(int $userId): bool
    public function getBannedUsers(): array
    public function banUser(int $userId): bool
    public function unbanUser(int $userId): bool
    public function isBanned(int $userId): bool
    
    // Channel management
    public function getChannels(): array
    public function addChannel(string $channel): bool
    public function removeChannel(string $channel): bool
    
    // Settings and statistics
    public function getSettings(): array
    public function updateSetting(string $key, mixed $value): bool
    public function getStatistics(): array
    public function updateStatistics(string $key, mixed $value): bool
    public function incrementStatistic(string $key): bool
    
    // Messages
    public function getWelcomeMessage(): string
    public function setWelcomeMessage(string $message): bool
    public function getAutoReplyMessage(): string
    public function setAutoReplyMessage(string $message): bool
    
    // Logging
    public function logMessage(string $level, string $message, array $context = []): bool
    public function cleanOldLogs(int $days = 30): void
}
```

### UserService Class

Manages user operations and permissions.

```php
namespace TelegramBot\Services;

class UserService
{
    public function __construct(FileService $fileService, TelegramAPI $api)
    
    // User registration and info
    public function registerUser(int $userId, array $userData = []): bool
    public function getUserInfo(int $userId): array|null
    public function getUserStatistics(): array
    
    // Permission checks
    public function isAdmin(int $userId): bool
    public function isMainAdmin(int $userId): bool
    public function isBanned(int $userId): bool
    public function hasPermission(int $userId, string $action): bool
    
    // Admin management
    public function addAdmin(int $userId, int $promotedBy): array
    public function removeAdmin(int $userId, int $removedBy): array
    public function getAdminList(): array
    
    // User moderation
    public function banUser(int $userId, int $bannedBy, string $reason = ''): array
    public function unbanUser(int $userId, int $unbannedBy): array
    
    // Promotion system
    public function generatePromotionCode(): string
    public function verifyPromotionCode(string $code): bool
    public function promoteWithCode(int $userId, string $code): array
    public function clearPromotionCode(): void
    
    // Rate limiting
    public function checkRateLimit(int $userId): bool
}
```

### MessageService Class

Handles message forwarding and management.

```php
namespace TelegramBot\Services;

class MessageService
{
    public function __construct(FileService $fileService, TelegramAPI $api, UserService $userService)
    
    // Message forwarding
    public function forwardToAdmin(array $message, int $adminChatId): bool
    public function sendReplyToUser(array $adminMessage, int $replyToMessageId): array
    
    // Message management
    public function editMessage(int $messageId, string $newText, int $chatId): array
    public function deleteMessage(int $messageId): array
    
    // Auto-reply
    public function sendAutoReply(int $chatId, int $messageId): array|false
    
    // Media handling
    public function isMediaAllowed(string $messageType): bool
    public function handleMediaRestriction(int $chatId, int $messageId, string $mediaType): array|false
    
    // Maintenance
    public function cleanOldMappings(int $days = 7): void
    public function getMessageStatistics(): array
}
```

### ValidationService Class

Provides input validation and security checks.

```php
namespace TelegramBot\Services;

class ValidationService
{
    // Basic validation
    public function validateUserId(int $userId): bool
    public function validateMessageText(string $text): bool
    public function sanitizeText(string $text): string
    public function validateChannelUsername(string $username): bool
    public function validatePromotionCode(string $code): bool
    public function validateCallbackData(string $data): bool
    public function validateJson(string $json): bool
    
    // Security checks
    public function isSpam(string $text): bool
    public function hasProhibitedLinks(string $text): bool
    public function hasSqlInjection(string $input): bool
    public function hasXss(string $input): bool
    
    // Rate limiting
    public function checkMessageRate(int $userId, int $timeWindow = 60, int $maxMessages = 20): bool
    public function logMessageForRateLimit(int $userId): void
    
    // Comprehensive validation
    public function validateInput(string $input, string $context = 'general'): array
    public function validateBroadcastMessage(string $message): array
    public function validateAdminInput(string $input, string $type): string|false
    public function validateFileUpload(array $fileInfo): bool
}
```

## Controllers

### AdminController Class

Handles admin panel functionality.

```php
namespace TelegramBot\Controllers;

class AdminController
{
    public function __construct(TelegramAPI $api, FileService $fileService, UserService $userService, ValidationService $validationService)
    
    // Main admin panel
    public function showAdminPanel(int $chatId, int $messageId = null): array|false
    
    // Admin management
    public function handleAdminManagement(int $chatId, int $messageId, string $action = 'main'): array|false
    
    // Subscription management
    public function handleSubscriptionManagement(int $chatId, int $messageId, string $action = 'main'): array|false
    
    // Statistics
    public function showStatistics(int $chatId, int $messageId): array|false
    
    // Media protection
    public function handleMediaProtection(int $chatId, int $messageId, string $action = 'main'): array|false
    
    // Settings
    public function toggleBotStatus(int $chatId, int $messageId): array|false
    public function toggleNotifications(int $chatId, int $messageId): array|false
}
```

### BroadcastController Class

Manages broadcasting functionality.

```php
namespace TelegramBot\Controllers;

class BroadcastController
{
    public function __construct(TelegramAPI $api, FileService $fileService, UserService $userService, ValidationService $validationService)
    
    // Broadcast panel
    public function showBroadcastPanel(int $chatId, int $messageId): array|false
    
    // Broadcast operations
    public function startBroadcast(int $chatId, int $messageId, string $type): array|false
    public function processBroadcast(array $message, string $type): bool
    public function deleteBroadcast(int $chatId, int $messageId, string $broadcastId): array|false
    public function cancelBroadcast(int $chatId, int $messageId): array|false
    
    // State management
    public function getBroadcastState(): array
    public function isInBroadcastMode(int $userId): bool
    
    // Statistics and maintenance
    public function getBroadcastStatistics(): array
    public function cleanOldBroadcasts(int $days = 7): void
}
```

### UserController Class

Handles user interactions and commands.

```php
namespace TelegramBot\Controllers;

class UserController
{
    public function __construct(TelegramAPI $api, FileService $fileService, UserService $userService, MessageService $messageService, ValidationService $validationService)
    
    // User commands
    public function handleStart(array $message): array|false
    public function handleUserMessage(array $message): bool
}
```

### GameController Class

Manages games and applications menu.

```php
namespace TelegramBot\Controllers;

class GameController
{
    public function __construct(TelegramAPI $api, FileService $fileService)
    
    // Menu navigation
    public function showMainMenu(int $chatId, int $messageId = null): array|false
    public function showGamesMenu(int $chatId, int $messageId): array|false
    public function showAppsMenu(int $chatId, int $messageId): array|false
    
    // Category display
    public function showGameCategory(int $chatId, int $messageId, string $category): array|false
    public function showAppCategory(int $chatId, int $messageId, string $category): array|false
    
    // Item details
    public function showGameDetails(int $chatId, int $messageId, string $gameId): array|false
    public function showAppDetails(int $chatId, int $messageId, string $appId): array|false
    
    // Text-based requests
    public function handleTextRequest(int $chatId, string $text): array|false
    
    // Special sections
    public function showOfflineGames(int $chatId, int $messageId): array|false
    public function showPaidServices(int $chatId, int $messageId): array|false
}
```

## Middleware

### AuthMiddleware Class

Handles authentication and authorization.

```php
namespace TelegramBot\Middleware;

class AuthMiddleware
{
    public function __construct(UserService $userService, FileService $fileService)
    
    // Authentication checks
    public function isAuthenticated(int $userId): bool
    public function isAdmin(int $userId): bool
    public function isMainAdmin(int $userId): bool
    public function hasPermission(int $userId, string $permission): bool
    
    // Middleware functions
    public function authenticate(array $update, callable $next): mixed
    public function requireAdmin(array $update, callable $next): mixed
    public function requireMainAdmin(array $update, callable $next): mixed
    public function requirePermission(string $permission): callable
    
    // User management
    public function canManageUser(int $adminId, int $targetId): bool
    
    // Session management
    public function isValidSession(int $userId): bool
    public function createSession(int $userId, int $duration = 3600): bool
    public function destroySession(int $userId): bool
    public function cleanExpiredSessions(): void
    
    // Permissions
    public function getUserPermissions(int $userId): array
    public function requiresElevatedPrivileges(string $action): bool
}
```

### SubscriptionMiddleware Class

Manages forced subscription functionality.

```php
namespace TelegramBot\Middleware;

class SubscriptionMiddleware
{
    public function __construct(TelegramAPI $api, FileService $fileService, UserService $userService)
    
    // Subscription checks
    public function isSubscriptionCheckEnabled(): bool
    public function isUserSubscribed(int $userId): bool
    public function isSubscribedToChannel(int $userId, string $channel): bool
    
    // Channel management
    public function getRequiredChannels(): array
    public function addRequiredChannel(string $channel): bool
    public function removeRequiredChannel(string $channel): bool
    public function isBotAdminInChannel(string $channel): bool
    
    // Middleware
    public function enforceSubscription(array $update, callable $next): mixed
    
    // User interface
    public function sendSubscriptionMessage(int $chatId, int $userId = null): array|false
    public function handleSubscriptionCheck(array $callbackQuery): bool
    
    // Statistics
    public function getSubscriptionStatistics(): array
    public function bulkCheckSubscriptions(array $userIds): array
}
```

### SecurityMiddleware Class

Provides security validation and threat detection.

```php
namespace TelegramBot\Middleware;

class SecurityMiddleware
{
    public function __construct(ValidationService $validationService, FileService $fileService)
    
    // Main security middleware
    public function validateSecurity(array $update, callable $next): mixed
    
    // Content validation
    private function validateMessage(array $message): bool
    private function validateCallbackQuery(array $callbackQuery): bool
    private function validateMedia(array $message): bool
    
    // Security checks
    private function containsBlockedWords(string $text): bool
    private function containsSuspiciousPatterns(string $text): bool
    private function containsMaliciousLinks(string $text): bool
    private function detectSuspiciousActivity(array $update): bool
    
    // Management
    public function addBlockedWord(string $word): bool
    public function removeBlockedWord(string $word): bool
    public function getSecurityStatistics(): array
}
```

## Configuration

### Configuration Constants

```php
// Bot Configuration
define('BOT_TOKEN', 'your_bot_token');
define('MAIN_ADMIN_ID', your_user_id);
define('CHANNEL_USERNAME', '@your_channel');

// Feature Flags
define('ENABLE_SUBSCRIPTION_CHECK', true);
define('ENABLE_ADMIN_NOTIFICATIONS', true);
define('ENABLE_MESSAGE_LOGGING', true);
define('ENABLE_STATISTICS', true);

// Security Settings
define('MAX_MESSAGE_LENGTH', 4096);
define('RATE_LIMIT_MESSAGES', 20);
define('RATE_LIMIT_TIME', 60);

// Messages
define('WELCOME_MESSAGE', 'Welcome message text');
define('AUTO_REPLY_MESSAGE', 'Auto reply text');
define('BANNED_MESSAGE', 'Banned user message');
define('SUBSCRIPTION_REQUIRED_MESSAGE', 'Subscription required text');
```

### File Configuration Array

```php
$config = [
    'files' => [
        'members' => USERS_PATH . 'members.txt',
        'admins' => ADMIN_PATH . 'admins.txt',
        'banned' => USERS_PATH . 'banned.txt',
        'channels' => ADMIN_PATH . 'channels.txt',
        'settings' => ADMIN_PATH . 'settings.json',
        'statistics' => ADMIN_PATH . 'statistics.json',
        'welcome_message' => ADMIN_PATH . 'welcome.txt',
        'auto_reply' => ADMIN_PATH . 'auto_reply.txt'
    ],
    'directories' => [
        'storage' => STORAGE_PATH,
        'users' => USERS_PATH,
        'messages' => MESSAGES_PATH,
        'admin' => ADMIN_PATH,
        'logs' => LOGS_PATH,
        'temp' => STORAGE_PATH . 'temp/',
        'backups' => STORAGE_PATH . 'backups/'
    ]
];
```

## Error Handling

All methods that can fail return either `false` or an array with error information:

```php
// Success response
['success' => true, 'message' => 'Operation completed']

// Error response
['success' => false, 'message' => 'Error description']
```

## Logging

The bot uses a comprehensive logging system:

```php
// Log levels: info, warning, error, security, debug
$fileService->logMessage('info', 'Message text', ['context' => 'data']);
```

Log files are stored in `storage/logs/` with daily rotation.
