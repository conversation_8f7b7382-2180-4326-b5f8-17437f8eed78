<?php

namespace TelegramBot\Controllers;

use TelegramBot\TelegramAPI;
use TelegramBot\Services\FileService;
use TelegramBot\Services\UserService;
use TelegramBot\Services\ValidationService;

/**
 * Admin Controller Class
 * 
 * Handles all admin panel functionality including user management,
 * settings configuration, and administrative commands.
 */
class AdminController
{
    private $api;
    private $fileService;
    private $userService;
    private $validationService;

    public function __construct(TelegramAPI $api, FileService $fileService, UserService $userService, ValidationService $validationService)
    {
        $this->api = $api;
        $this->fileService = $fileService;
        $this->userService = $userService;
        $this->validationService = $validationService;
    }

    /**
     * Show main admin panel
     */
    public function showAdminPanel($chatId, $messageId = null)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => 'اخر تحديثات البوت 🧬', 'url' => 't.me/VEVoGamez']],
            [
                ['text' => 'عمل البوت', 'callback_data' => 'admin_bot_status'],
                ['text' => 'اشعارات الدخول', 'callback_data' => 'admin_notifications']
            ],
            [['text' => 'قسم الاشتراك الاجباري', 'callback_data' => 'admin_subscription']],
            [['text' => 'قسم الاذاعة', 'callback_data' => 'admin_broadcast']],
            [['text' => 'قسم الاحصائيات', 'callback_data' => 'admin_statistics']],
            [['text' => 'قسم الادمنية', 'callback_data' => 'admin_management']],
            [['text' => 'إعدادات الرسائل', 'callback_data' => 'admin_messages']],
            [['text' => 'حماية الوسائط', 'callback_data' => 'admin_media_protection']]
        ]);

        $text = "*• اهلا بك في لوحة الأدمن الخاصة بالبوت 🤖\n\n- يمكنك التحكم في البوت الخاص بك من هنا*";

        if ($messageId) {
            return $this->api->editMessageText($chatId, $messageId, $text, [
                'reply_markup' => $keyboard,
                'parse_mode' => 'Markdown'
            ]);
        } else {
            return $this->api->sendMessage($chatId, $text, [
                'reply_markup' => $keyboard,
                'parse_mode' => 'Markdown'
            ]);
        }
    }

    /**
     * Handle admin management section
     */
    public function handleAdminManagement($chatId, $messageId, $action = 'main')
    {
        switch ($action) {
            case 'main':
                return $this->showAdminManagementPanel($chatId, $messageId);
            case 'add':
                return $this->showAddAdminPanel($chatId, $messageId);
            case 'remove':
                return $this->showRemoveAdminPanel($chatId, $messageId);
            case 'list':
                return $this->showAdminList($chatId, $messageId);
            default:
                return $this->showAdminPanel($chatId, $messageId);
        }
    }

    /**
     * Show admin management panel
     */
    private function showAdminManagementPanel($chatId, $messageId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [
                ['text' => 'اضافة ادمن', 'callback_data' => 'admin_management_add'],
                ['text' => 'تنزيل ادمن', 'callback_data' => 'admin_management_remove']
            ],
            [['text' => 'قائمة الادمنية', 'callback_data' => 'admin_management_list']],
            [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
        ]);

        $text = "• مرحبا بك في إدارة الأدمنية 👮‍♀️\n\n- يمكنك رفع " . MAX_ADMINS . " أدمن في البوت أو حذفهم\n- يمكن للأدمنية التحكم في لوحة البوت مثلك ولا يمكنهم رفع أدمنية جديدة";

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Show add admin panel
     */
    private function showAddAdminPanel($chatId, $messageId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_management']]
        ]);

        $text = "لإضافة أدمن جديد، أرسل الأمر:\n`ترقية`\n\nسيتم إنشاء رمز ترقية يمكن للمستخدم استخدامه";

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard,
            'parse_mode' => 'Markdown'
        ]);
    }

    /**
     * Show remove admin panel
     */
    private function showRemoveAdminPanel($chatId, $messageId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_management']]
        ]);

        $text = "لتنزيل أدمن، أرسل الأمر:\n`تنزيل [معرف المستخدم]`\n\nمثال: `تنزيل 123456789`";

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard,
            'parse_mode' => 'Markdown'
        ]);
    }

    /**
     * Show admin list
     */
    private function showAdminList($chatId, $messageId)
    {
        $admins = $this->userService->getAdminList();
        
        $text = "📋 قائمة الأدمنية:\n\n";
        
        if (empty($admins)) {
            $text .= "لا يوجد أدمنية حالياً";
        } else {
            foreach ($admins as $index => $admin) {
                $num = $index + 1;
                $name = $admin['first_name'];
                $id = $admin['id'];
                $status = $admin['is_main_admin'] ? '👑' : '👮‍♀️';
                
                $text .= "$num. $status [$name](tg://user?id=$id) - `$id`\n";
            }
        }

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_management']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard,
            'parse_mode' => 'Markdown'
        ]);
    }

    /**
     * Handle subscription management
     */
    public function handleSubscriptionManagement($chatId, $messageId, $action = 'main')
    {
        switch ($action) {
            case 'main':
                return $this->showSubscriptionPanel($chatId, $messageId);
            case 'add_channel':
                return $this->showAddChannelPanel($chatId, $messageId);
            case 'remove_channel':
                return $this->showRemoveChannelPanel($chatId, $messageId);
            case 'list_channels':
                return $this->showChannelsList($chatId, $messageId);
            default:
                return $this->showAdminPanel($chatId, $messageId);
        }
    }

    /**
     * Show subscription management panel
     */
    private function showSubscriptionPanel($chatId, $messageId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [
                ['text' => 'تعيين قناة', 'callback_data' => 'admin_subscription_add_channel'],
                ['text' => 'مسح قناة', 'callback_data' => 'admin_subscription_remove_channel']
            ],
            [['text' => 'عرض القنوات', 'callback_data' => 'admin_subscription_list_channels']],
            [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
        ]);

        $text = "*• مرحبا بك في قسم الاشتراك الاجباري 💫*";

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard,
            'parse_mode' => 'Markdown'
        ]);
    }

    /**
     * Show add channel panel
     */
    private function showAddChannelPanel($chatId, $messageId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_subscription']]
        ]);

        $text = "*• قم برفع البوت أدمن في قناتك أولاً 🌟\n\n• من ثم أرسل معرف القناة إلى البوت*";

        // Set state for channel addition
        $this->fileService->writeFile(ADMIN_PATH . 'state.txt', 'adding_channel');

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard,
            'parse_mode' => 'Markdown'
        ]);
    }

    /**
     * Show statistics panel
     */
    public function showStatistics($chatId, $messageId)
    {
        $stats = $this->userService->getUserStatistics();
        
        $text = "📊 إحصائيات البوت:\n\n";
        $text .= "👥 إجمالي المستخدمين: " . $stats['total_users'] . "\n";
        $text .= "👮‍♀️ عدد الأدمنية: " . $stats['total_admins'] . "\n";
        $text .= "🚫 المحظورين: " . $stats['total_banned'] . "\n";
        $text .= "📩 الرسائل المستلمة: " . $stats['total_messages_received'] . "\n";
        $text .= "📬 الرسائل المرسلة: " . $stats['total_messages_sent'] . "\n";
        $text .= "📢 الإذاعات: " . $stats['total_broadcasts'] . "\n";
        $text .= "🕐 تاريخ التشغيل: " . $stats['bot_started'];

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Handle media protection settings
     */
    public function handleMediaProtection($chatId, $messageId, $action = 'main')
    {
        switch ($action) {
            case 'main':
                return $this->showMediaProtectionPanel($chatId, $messageId);
            default:
                return $this->toggleMediaRestriction($chatId, $messageId, $action);
        }
    }

    /**
     * Show media protection panel
     */
    private function showMediaProtectionPanel($chatId, $messageId)
    {
        $settings = $this->fileService->getSettings();
        $restrictions = $settings['media_restrictions'] ?? [];

        $getStatus = function($type) use ($restrictions) {
            return ($restrictions[$type] ?? false) ? '🔒' : '🔓';
        };

        $keyboard = TelegramAPI::createInlineKeyboard([
            [
                ['text' => $getStatus('photos') . ' الصور', 'callback_data' => 'admin_media_photos'],
                ['text' => $getStatus('videos') . ' الفيديو', 'callback_data' => 'admin_media_videos']
            ],
            [
                ['text' => $getStatus('documents') . ' الملفات', 'callback_data' => 'admin_media_documents'],
                ['text' => $getStatus('stickers') . ' الملصقات', 'callback_data' => 'admin_media_stickers']
            ],
            [
                ['text' => $getStatus('voice') . ' الصوتيات', 'callback_data' => 'admin_media_voice'],
                ['text' => $getStatus('audio') . ' الموسيقى', 'callback_data' => 'admin_media_audio']
            ],
            [['text' => $getStatus('forwards') . ' التوجيه', 'callback_data' => 'admin_media_forwards']],
            [
                ['text' => '🔒 قفل الكل', 'callback_data' => 'admin_media_lock_all'],
                ['text' => '🔓 فتح الكل', 'callback_data' => 'admin_media_unlock_all']
            ],
            [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
        ]);

        $text = "🛡️ إعدادات حماية الوسائط\n\n🔒 = مقفل | 🔓 = مفتوح\n\nاضغط على أي نوع لتغيير حالته";

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Toggle media restriction
     */
    private function toggleMediaRestriction($chatId, $messageId, $mediaType)
    {
        $settings = $this->fileService->getSettings();
        $restrictions = $settings['media_restrictions'] ?? [];

        if ($mediaType === 'lock_all') {
            $restrictions = [
                'photos' => true,
                'videos' => true,
                'documents' => true,
                'stickers' => true,
                'voice' => true,
                'audio' => true,
                'forwards' => true
            ];
        } elseif ($mediaType === 'unlock_all') {
            $restrictions = [
                'photos' => false,
                'videos' => false,
                'documents' => false,
                'stickers' => false,
                'voice' => false,
                'audio' => false,
                'forwards' => false
            ];
        } else {
            $restrictions[$mediaType] = !($restrictions[$mediaType] ?? false);
        }

        $settings['media_restrictions'] = $restrictions;
        $this->fileService->updateSetting('media_restrictions', $restrictions);

        return $this->handleMediaProtection($chatId, $messageId, 'main');
    }

    /**
     * Handle bot status toggle
     */
    public function toggleBotStatus($chatId, $messageId)
    {
        $settings = $this->fileService->getSettings();
        $isEnabled = $settings['bot_enabled'] ?? true;
        
        $settings['bot_enabled'] = !$isEnabled;
        $this->fileService->updateSetting('bot_enabled', !$isEnabled);

        $status = $settings['bot_enabled'] ? 'تم تفعيل' : 'تم تعطيل';
        $text = "$status البوت ✅";

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Toggle notifications
     */
    public function toggleNotifications($chatId, $messageId)
    {
        $settings = $this->fileService->getSettings();
        $isEnabled = $settings['notifications_enabled'] ?? true;
        
        $settings['notifications_enabled'] = !$isEnabled;
        $this->fileService->updateSetting('notifications_enabled', !$isEnabled);

        $status = $settings['notifications_enabled'] ? 'تم تفعيل' : 'تم تعطيل';
        $text = "$status إشعارات الدخول ✅";

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Handle message settings
     */
    public function handleMessageSettings($chatId, $messageId)
    {
        $settings = $this->fileService->getSettings();
        $welcomeMsg = $this->fileService->getWelcomeMessage();
        $autoReplyMsg = $this->fileService->getAutoReplyMessage();

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => 'تعديل رسالة الترحيب', 'callback_data' => 'admin_edit_welcome']],
            [['text' => 'تعديل الرد التلقائي', 'callback_data' => 'admin_edit_auto_reply']],
            [
                ['text' => ($settings['auto_reply_enabled'] ?? true) ? '🔴 إيقاف الرد التلقائي' : '🟢 تفعيل الرد التلقائي',
                 'callback_data' => 'admin_toggle_auto_reply']
            ],
            [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
        ]);

        $text = "⚙️ إعدادات الرسائل\n\n";
        $text .= "📝 رسالة الترحيب الحالية:\n" . substr($welcomeMsg, 0, 100) . "...\n\n";
        $text .= "🤖 الرد التلقائي الحالي:\n" . substr($autoReplyMsg, 0, 100) . "...\n\n";
        $text .= "حالة الرد التلقائي: " . (($settings['auto_reply_enabled'] ?? true) ? "🟢 مفعل" : "🔴 معطل");

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Show remove channel panel
     */
    private function showRemoveChannelPanel($chatId, $messageId)
    {
        $channels = $this->fileService->getChannels();

        if (empty($channels)) {
            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '• رجوع •', 'callback_data' => 'admin_subscription']]
            ]);

            return $this->api->editMessageText($chatId, $messageId,
                "لا توجد قنوات مضافة حالياً", [
                'reply_markup' => $keyboard
            ]);
        }

        $text = "اختر القناة المراد حذفها:\n\n";
        $buttons = [];

        foreach ($channels as $index => $channel) {
            if (!empty($channel)) {
                $buttons[] = [['text' => $channel, 'callback_data' => "admin_remove_channel_$index"]];
            }
        }

        $buttons[] = [['text' => '• رجوع •', 'callback_data' => 'admin_subscription']];
        $keyboard = TelegramAPI::createInlineKeyboard($buttons);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Show channels list
     */
    private function showChannelsList($chatId, $messageId)
    {
        $channels = $this->fileService->getChannels();

        $text = "📋 قائمة القنوات المضافة:\n\n";

        if (empty($channels)) {
            $text .= "لا توجد قنوات مضافة حالياً";
        } else {
            foreach ($channels as $index => $channel) {
                if (!empty($channel)) {
                    $num = $index + 1;
                    $text .= "$num. $channel\n";
                }
            }
        }

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_subscription']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Toggle auto reply
     */
    public function toggleAutoReply($chatId, $messageId)
    {
        $settings = $this->fileService->getSettings();
        $isEnabled = $settings['auto_reply_enabled'] ?? true;

        $this->fileService->updateSetting('auto_reply_enabled', !$isEnabled);

        $status = !$isEnabled ? 'تم تفعيل' : 'تم تعطيل';
        $text = "$status الرد التلقائي ✅";

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_messages']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Remove channel
     */
    public function removeChannel($chatId, $messageId, $channelIndex)
    {
        $channels = $this->fileService->getChannels();

        if (isset($channels[$channelIndex])) {
            $removedChannel = $channels[$channelIndex];
            $this->fileService->removeChannel($removedChannel);

            $text = "تم حذف القناة: $removedChannel ✅";
        } else {
            $text = "❌ لم يتم العثور على القناة";
        }

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'admin_subscription']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }
}
