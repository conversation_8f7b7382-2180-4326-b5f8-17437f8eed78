<?php
/**
 * FileService Test Script
 * 
 * This script tests if the FileService works correctly with the configuration.
 */

echo "🧪 Testing FileService\n";
echo "======================\n\n";

// Load configuration
echo "Loading configuration... ";
try {
    $config = require __DIR__ . '/../config/config.php';
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Create FileService with config
echo "Test 1: Creating FileService with config... ";
try {
    require_once __DIR__ . '/../src/Services/FileService.php';
    $fileService = new TelegramBot\Services\FileService($config);
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 2: Create FileService without config (should use defaults)
echo "Test 2: Creating FileService without config... ";
try {
    $fileServiceNoConfig = new TelegramBot\Services\FileService();
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 3: Test basic file operations
echo "Test 3: Testing basic file operations... ";
try {
    // Test getting statistics (this was causing the original error)
    $stats = $fileService->getStatistics();
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 4: Test admin operations
echo "Test 4: Testing admin operations... ";
try {
    $admins = $fileService->getAdmins();
    echo "✅ SUCCESS (Found " . count($admins) . " admins)\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 5: Test member operations
echo "Test 5: Testing member operations... ";
try {
    $members = $fileService->getMembers();
    echo "✅ SUCCESS (Found " . count($members) . " members)\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 6: Test banned users operations
echo "Test 6: Testing banned users operations... ";
try {
    $banned = $fileService->getBannedUsers();
    echo "✅ SUCCESS (Found " . count($banned) . " banned users)\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 7: Test settings operations
echo "Test 7: Testing settings operations... ";
try {
    $settings = $fileService->getSettings();
    echo "✅ SUCCESS (Found " . count($settings) . " settings)\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 8: Test channels operations
echo "Test 8: Testing channels operations... ";
try {
    $channels = $fileService->getChannels();
    echo "✅ SUCCESS (Found " . count($channels) . " channels)\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 9: Test message operations
echo "Test 9: Testing message operations... ";
try {
    $welcomeMsg = $fileService->getWelcomeMessage();
    $autoReplyMsg = $fileService->getAutoReplyMessage();
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 10: Test logging
echo "Test 10: Testing logging... ";
try {
    $fileService->logMessage('info', 'Test log message from FileService test');
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 11: Test with null config (edge case)
echo "Test 11: Testing with null config... ";
try {
    $fileServiceNull = new TelegramBot\Services\FileService(null);
    $stats = $fileServiceNull->getStatistics();
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 12: Test with empty config (edge case)
echo "Test 12: Testing with empty config... ";
try {
    $fileServiceEmpty = new TelegramBot\Services\FileService([]);
    $stats = $fileServiceEmpty->getStatistics();
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

echo "\n======================\n";
echo "FileService test completed!\n";
echo "If all tests passed, the FileService is working correctly.\n";
echo "======================\n";
