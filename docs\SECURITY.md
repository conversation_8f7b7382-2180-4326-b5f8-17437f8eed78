# Security Guide

This document outlines the security features and best practices for the VEVoGamez Telegram Bot.

## Table of Contents

- [Security Architecture](#security-architecture)
- [Built-in Security Features](#built-in-security-features)
- [Input Validation](#input-validation)
- [Authentication & Authorization](#authentication--authorization)
- [Rate Limiting](#rate-limiting)
- [Data Protection](#data-protection)
- [Security Best Practices](#security-best-practices)
- [Monitoring & Logging](#monitoring--logging)
- [Incident Response](#incident-response)

## Security Architecture

### Multi-Layer Security

The bot implements a multi-layer security approach:

1. **Network Layer**: HTTPS/TLS encryption for all communications
2. **Application Layer**: Input validation, authentication, and authorization
3. **Data Layer**: File permissions and data sanitization
4. **Monitoring Layer**: Comprehensive logging and threat detection

### Security Middleware

The bot uses middleware to enforce security policies:

```php
// Security middleware pipeline
SecurityMiddleware -> AuthMiddleware -> SubscriptionMiddleware -> Controller
```

## Built-in Security Features

### 1. Input Validation

All user inputs are validated and sanitized:

```php
// Example validation
$validationService = new ValidationService();

// Validate user ID
if (!$validationService->validateUserId($userId)) {
    return false; // Block invalid user ID
}

// Validate message content
if (!$validationService->validateMessageText($text)) {
    return false; // Block invalid message
}

// Check for malicious content
if ($validationService->hasSqlInjection($input) || 
    $validationService->hasXss($input)) {
    return false; // Block malicious input
}
```

### 2. Threat Detection

The bot automatically detects and blocks:

- **SQL Injection attempts**
- **XSS attacks**
- **Spam patterns**
- **Malicious links**
- **Suspicious file uploads**
- **Bot-like behavior**

### 3. Rate Limiting

Prevents abuse through rate limiting:

```php
// Rate limiting configuration
define('RATE_LIMIT_MESSAGES', 20); // Max messages per minute
define('RATE_LIMIT_TIME', 60); // Time window in seconds

// Check rate limit
if (!$userService->checkRateLimit($userId)) {
    // Block user temporarily
    return false;
}
```

### 4. File Security

Secure file handling:

- **File type validation**
- **File size limits**
- **Safe filename generation**
- **Restricted file access**

## Input Validation

### Message Validation

```php
class ValidationService
{
    public function validateMessageText($text)
    {
        // Check length
        if (strlen($text) > MAX_MESSAGE_LENGTH) {
            return false;
        }
        
        // Check for spam patterns
        if ($this->isSpam($text)) {
            return false;
        }
        
        // Check for malicious links
        if ($this->hasProhibitedLinks($text)) {
            return false;
        }
        
        return true;
    }
    
    public function isSpam($text)
    {
        $patterns = [
            '/(.)\1{10,}/', // Repeated characters
            '/[A-Z]{20,}/', // Too many capitals
            '/(.{1,10})\1{5,}/', // Repeated patterns
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }
        
        return false;
    }
}
```

### File Upload Validation

```php
public function validateFileUpload($fileInfo)
{
    // Check file size
    if (isset($fileInfo['file_size']) && 
        $fileInfo['file_size'] > 50 * 1024 * 1024) { // 50MB
        return false;
    }
    
    // Check MIME type
    if (isset($fileInfo['mime_type']) && 
        $this->isSuspiciousMimeType($fileInfo['mime_type'])) {
        return false;
    }
    
    return true;
}

private function isSuspiciousMimeType($mimeType)
{
    $suspicious = [
        'application/x-executable',
        'application/x-msdownload',
        'application/x-bat',
        'text/x-script.phps'
    ];
    
    return in_array($mimeType, $suspicious);
}
```

## Authentication & Authorization

### User Authentication

```php
class AuthMiddleware
{
    public function authenticate($update, $next)
    {
        $userId = $this->extractUserId($update);
        
        // Check if user is banned
        if ($this->userService->isBanned($userId)) {
            return $this->denyAccess('User is banned');
        }
        
        // Check rate limiting
        if (!$this->checkRateLimit($userId)) {
            return $this->denyAccess('Rate limit exceeded');
        }
        
        return $next($update);
    }
}
```

### Permission System

```php
// Permission levels
const PERMISSIONS = [
    'send_message' => 0,
    'admin_panel' => 1,
    'manage_users' => 2,
    'manage_admins' => 3,
    'system_admin' => 4
];

public function hasPermission($userId, $permission)
{
    $userLevel = $this->getUserPermissionLevel($userId);
    $requiredLevel = PERMISSIONS[$permission] ?? 999;
    
    return $userLevel >= $requiredLevel;
}
```

### Session Management

```php
public function createSession($userId, $duration = 3600)
{
    $sessionData = [
        'user_id' => $userId,
        'created_at' => time(),
        'expires_at' => time() + $duration,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'token' => bin2hex(random_bytes(32))
    ];
    
    return $this->saveSession($sessionData);
}

public function validateSession($userId, $token)
{
    $session = $this->getSession($userId);
    
    if (!$session || $session['token'] !== $token) {
        return false;
    }
    
    if ($session['expires_at'] < time()) {
        $this->destroySession($userId);
        return false;
    }
    
    return true;
}
```

## Rate Limiting

### Implementation

```php
class RateLimiter
{
    private $limits = [
        'message' => ['count' => 20, 'window' => 60],
        'callback' => ['count' => 50, 'window' => 60],
        'command' => ['count' => 10, 'window' => 60]
    ];
    
    public function checkLimit($userId, $type = 'message')
    {
        $limit = $this->limits[$type];
        $key = "rate_limit_{$type}_{$userId}";
        
        $requests = $this->getRequests($key);
        $currentTime = time();
        
        // Remove old requests
        $requests = array_filter($requests, function($timestamp) use ($currentTime, $limit) {
            return ($currentTime - $timestamp) < $limit['window'];
        });
        
        // Check if limit exceeded
        if (count($requests) >= $limit['count']) {
            return false;
        }
        
        // Add current request
        $requests[] = $currentTime;
        $this->saveRequests($key, $requests);
        
        return true;
    }
}
```

### Progressive Rate Limiting

```php
public function getProgressiveDelay($userId, $violations)
{
    $delays = [
        1 => 60,    // 1 minute
        2 => 300,   // 5 minutes
        3 => 900,   // 15 minutes
        4 => 3600,  // 1 hour
        5 => 86400  // 24 hours
    ];
    
    return $delays[min($violations, 5)] ?? 86400;
}
```

## Data Protection

### Data Encryption

```php
class DataProtection
{
    private $encryptionKey;
    
    public function __construct()
    {
        $this->encryptionKey = $this->getEncryptionKey();
    }
    
    public function encrypt($data)
    {
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt(
            $data, 
            'AES-256-CBC', 
            $this->encryptionKey, 
            0, 
            $iv
        );
        
        return base64_encode($iv . $encrypted);
    }
    
    public function decrypt($encryptedData)
    {
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt(
            $encrypted, 
            'AES-256-CBC', 
            $this->encryptionKey, 
            0, 
            $iv
        );
    }
}
```

### Secure File Storage

```php
// File permissions
chmod($sensitiveFile, 0600); // Owner read/write only

// Secure file paths
$safePath = realpath($basePath . '/' . basename($userInput));
if (strpos($safePath, $basePath) !== 0) {
    throw new SecurityException('Path traversal attempt');
}

// File content validation
$content = file_get_contents($file);
if ($this->containsMaliciousContent($content)) {
    unlink($file);
    throw new SecurityException('Malicious file detected');
}
```

## Security Best Practices

### 1. Server Configuration

#### Apache (.htaccess)
```apache
# Deny access to sensitive files
<Files "config.php">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

# Security headers
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-XSS-Protection "1; mode=block"
Header always set X-Content-Type-Options "nosniff"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
```

#### Nginx
```nginx
# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# Deny access to sensitive files
location ~ /\. {
    deny all;
}

location ~ \.(log|txt)$ {
    deny all;
}
```

### 2. PHP Configuration

```ini
; php.ini security settings
expose_php = Off
display_errors = Off
log_errors = On
error_log = /path/to/secure/error.log

; File upload security
file_uploads = On
upload_max_filesize = 50M
max_file_uploads = 5

; Session security
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1
```

### 3. Environment Variables

```bash
# Use environment variables for sensitive data
export BOT_TOKEN="your_bot_token"
export ENCRYPTION_KEY="your_encryption_key"
export DATABASE_PASSWORD="your_db_password"
```

```php
// Access in PHP
$botToken = $_ENV['BOT_TOKEN'] ?? getenv('BOT_TOKEN');
```

### 4. Regular Updates

- Keep PHP updated to the latest stable version
- Update server software regularly
- Monitor security advisories
- Update dependencies

### 5. Backup Security

```bash
# Encrypt backups
tar -czf - /path/to/bot | gpg --cipher-algo AES256 --compress-algo 1 --symmetric --output backup.tar.gz.gpg

# Secure backup storage
chmod 600 backup.tar.gz.gpg
chown root:root backup.tar.gz.gpg
```

## Monitoring & Logging

### Security Event Logging

```php
class SecurityLogger
{
    public function logSecurityEvent($event, $userId, $details = [])
    {
        $logEntry = [
            'timestamp' => time(),
            'event' => $event,
            'user_id' => $userId,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'details' => $details
        ];
        
        $this->writeSecurityLog($logEntry);
        
        // Alert on critical events
        if ($this->isCriticalEvent($event)) {
            $this->sendSecurityAlert($logEntry);
        }
    }
    
    private function isCriticalEvent($event)
    {
        $criticalEvents = [
            'sql_injection_attempt',
            'xss_attempt',
            'multiple_failed_auth',
            'admin_privilege_escalation'
        ];
        
        return in_array($event, $criticalEvents);
    }
}
```

### Real-time Monitoring

```php
// Monitor for suspicious patterns
public function detectAnomalies()
{
    $recentLogs = $this->getRecentSecurityLogs(3600); // Last hour
    
    $patterns = [
        'rapid_requests' => $this->countRapidRequests($recentLogs),
        'failed_auth' => $this->countFailedAuth($recentLogs),
        'suspicious_ips' => $this->detectSuspiciousIPs($recentLogs)
    ];
    
    foreach ($patterns as $pattern => $count) {
        if ($count > $this->getThreshold($pattern)) {
            $this->triggerAlert($pattern, $count);
        }
    }
}
```

## Incident Response

### 1. Incident Detection

```php
class IncidentDetector
{
    public function detectIncident($event)
    {
        $severity = $this->calculateSeverity($event);
        
        if ($severity >= SEVERITY_HIGH) {
            $this->initiateIncidentResponse($event);
        }
    }
    
    private function calculateSeverity($event)
    {
        $severityMap = [
            'sql_injection' => 9,
            'xss_attack' => 8,
            'brute_force' => 7,
            'spam_flood' => 5,
            'rate_limit_exceeded' => 3
        ];
        
        return $severityMap[$event['type']] ?? 1;
    }
}
```

### 2. Automated Response

```php
public function initiateIncidentResponse($event)
{
    // 1. Log incident
    $this->logIncident($event);
    
    // 2. Block source if necessary
    if ($this->shouldBlockSource($event)) {
        $this->blockSource($event['source_ip']);
    }
    
    // 3. Notify administrators
    $this->notifyAdmins($event);
    
    // 4. Escalate if critical
    if ($event['severity'] >= SEVERITY_CRITICAL) {
        $this->escalateIncident($event);
    }
}
```

### 3. Recovery Procedures

```php
public function recoverFromIncident($incidentId)
{
    $incident = $this->getIncident($incidentId);
    
    switch ($incident['type']) {
        case 'data_breach':
            $this->rotateTokens();
            $this->notifyUsers();
            break;
            
        case 'service_disruption':
            $this->restoreService();
            $this->validateIntegrity();
            break;
            
        case 'unauthorized_access':
            $this->revokeAccess();
            $this->auditPermissions();
            break;
    }
    
    $this->markIncidentResolved($incidentId);
}
```

## Security Checklist

### Pre-Deployment
- [ ] All sensitive data encrypted
- [ ] Input validation implemented
- [ ] Rate limiting configured
- [ ] Security headers set
- [ ] File permissions configured
- [ ] Error handling implemented
- [ ] Logging configured

### Post-Deployment
- [ ] Security monitoring active
- [ ] Log analysis automated
- [ ] Backup procedures tested
- [ ] Incident response plan ready
- [ ] Regular security audits scheduled
- [ ] Staff security training completed

### Regular Maintenance
- [ ] Security logs reviewed weekly
- [ ] Failed login attempts monitored
- [ ] Rate limiting effectiveness checked
- [ ] File integrity verified
- [ ] Access permissions audited
- [ ] Security patches applied

## Emergency Contacts

In case of security incidents:

1. **Immediate Response**: Disable bot webhook
2. **Assessment**: Review logs and determine scope
3. **Containment**: Block malicious sources
4. **Recovery**: Restore from clean backup
5. **Communication**: Notify users if necessary

Remember: Security is an ongoing process, not a one-time setup!
