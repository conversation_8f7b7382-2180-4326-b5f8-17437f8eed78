<?php

namespace TelegramBot\Controllers;

use TelegramBot\TelegramAPI;
use TelegramBot\Services\FileService;

/**
 * Game Controller Class
 * 
 * Handles games and applications menu system with organized categories.
 */
class GameController
{
    private $api;
    private $fileService;

    public function __construct(TelegramAPI $api, FileService $fileService)
    {
        $this->api = $api;
        $this->fileService = $fileService;
    }

    /**
     * Show main games menu
     */
    public function showMainMenu($chatId, $messageId = null)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [
                ['text' => 'ألعاب', 'callback_data' => 'games_menu'],
                ['text' => 'تطبيقات', 'callback_data' => 'apps_menu']
            ],
            [
                ['text' => 'ألعاب بدون أنترنت', 'callback_data' => 'offline_games'],
                ['text' => 'خدمات مدفوعة', 'callback_data' => 'paid_services']
            ]
        ]);

        $text = "🎮 أنت الآن في القائمة الرئيسية\n\nيمكنك استخدام ما يلي:";

        if ($messageId) {
            return $this->api->editMessageText($chatId, $messageId, $text, [
                'reply_markup' => $keyboard
            ]);
        } else {
            return $this->api->sendMessage($chatId, $text, [
                'reply_markup' => $keyboard
            ]);
        }
    }

    /**
     * Show games menu
     */
    public function showGamesMenu($chatId, $messageId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [
                ['text' => 'العاب الحركة', 'callback_data' => 'games_action'],
                ['text' => 'العاب المحاكاة', 'callback_data' => 'games_simulation']
            ],
            [
                ['text' => 'العاب إستراتيجية', 'callback_data' => 'games_strategy'],
                ['text' => 'العاب الألغاز', 'callback_data' => 'games_puzzle']
            ],
            [
                ['text' => 'العاب كلاسيكية', 'callback_data' => 'games_classic'],
                ['text' => 'العاب المغامرات', 'callback_data' => 'games_adventure']
            ],
            [
                ['text' => 'العاب تقمص الأدوار', 'callback_data' => 'games_rpg'],
                ['text' => 'العاب الرياضة', 'callback_data' => 'games_sports']
            ],
            [
                ['text' => 'العاب خفيفة', 'callback_data' => 'games_casual'],
                ['text' => 'العاب سباق', 'callback_data' => 'games_racing']
            ],
            [['text' => '• رجوع •', 'callback_data' => 'main_menu']]
        ]);

        $text = "🎮 قسم الألعاب\n\nاختر نوع اللعبة المطلوبة:";

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Show apps menu
     */
    public function showAppsMenu($chatId, $messageId)
    {
        $keyboard = TelegramAPI::createInlineKeyboard([
            [
                ['text' => 'تطبيقات التواصل', 'callback_data' => 'apps_social'],
                ['text' => 'تطبيقات الإنتاجية', 'callback_data' => 'apps_productivity']
            ],
            [
                ['text' => 'تطبيقات التصوير', 'callback_data' => 'apps_photography'],
                ['text' => 'تطبيقات الموسيقى', 'callback_data' => 'apps_music']
            ],
            [
                ['text' => 'تطبيقات التعليم', 'callback_data' => 'apps_education'],
                ['text' => 'تطبيقات الأخبار', 'callback_data' => 'apps_news']
            ],
            [
                ['text' => 'تطبيقات الطقس', 'callback_data' => 'apps_weather'],
                ['text' => 'تطبيقات السفر', 'callback_data' => 'apps_travel']
            ],
            [['text' => '• رجوع •', 'callback_data' => 'main_menu']]
        ]);

        $text = "📱 قسم التطبيقات\n\nاختر نوع التطبيق المطلوب:";

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Show specific game category
     */
    public function showGameCategory($chatId, $messageId, $category)
    {
        $games = $this->getGamesByCategory($category);
        
        if (empty($games)) {
            $text = "🎮 لا توجد ألعاب متاحة في هذا القسم حالياً\n\nسيتم إضافة المزيد قريباً!";
            
            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '• رجوع •', 'callback_data' => 'games_menu']]
            ]);
        } else {
            $categoryNames = [
                'action' => 'ألعاب الحركة',
                'simulation' => 'ألعاب المحاكاة',
                'strategy' => 'ألعاب الإستراتيجية',
                'puzzle' => 'ألعاب الألغاز',
                'classic' => 'ألعاب كلاسيكية',
                'adventure' => 'ألعاب المغامرات',
                'rpg' => 'ألعاب تقمص الأدوار',
                'sports' => 'ألعاب الرياضة',
                'casual' => 'ألعاب خفيفة',
                'racing' => 'ألعاب السباق'
            ];

            $text = "🎮 " . ($categoryNames[$category] ?? 'ألعاب') . "\n\n";
            $text .= "اختر اللعبة المطلوبة:";

            $buttons = [];
            foreach ($games as $game) {
                $buttons[] = [['text' => $game['name'], 'callback_data' => 'game_' . $game['id']]];
            }
            $buttons[] = [['text' => '• رجوع •', 'callback_data' => 'games_menu']];

            $keyboard = TelegramAPI::createInlineKeyboard($buttons);
        }

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Show specific app category
     */
    public function showAppCategory($chatId, $messageId, $category)
    {
        $apps = $this->getAppsByCategory($category);
        
        if (empty($apps)) {
            $text = "📱 لا توجد تطبيقات متاحة في هذا القسم حالياً\n\nسيتم إضافة المزيد قريباً!";
            
            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '• رجوع •', 'callback_data' => 'apps_menu']]
            ]);
        } else {
            $categoryNames = [
                'social' => 'تطبيقات التواصل',
                'productivity' => 'تطبيقات الإنتاجية',
                'photography' => 'تطبيقات التصوير',
                'music' => 'تطبيقات الموسيقى',
                'education' => 'تطبيقات التعليم',
                'news' => 'تطبيقات الأخبار',
                'weather' => 'تطبيقات الطقس',
                'travel' => 'تطبيقات السفر'
            ];

            $text = "📱 " . ($categoryNames[$category] ?? 'تطبيقات') . "\n\n";
            $text .= "اختر التطبيق المطلوب:";

            $buttons = [];
            foreach ($apps as $app) {
                $buttons[] = [['text' => $app['name'], 'callback_data' => 'app_' . $app['id']]];
            }
            $buttons[] = [['text' => '• رجوع •', 'callback_data' => 'apps_menu']];

            $keyboard = TelegramAPI::createInlineKeyboard($buttons);
        }

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Show game details
     */
    public function showGameDetails($chatId, $messageId, $gameId)
    {
        $game = $this->getGameById($gameId);
        
        if (!$game) {
            $text = "❌ اللعبة غير موجودة";
            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '• رجوع •', 'callback_data' => 'games_menu']]
            ]);
        } else {
            $text = "🎮 **{$game['name']}**\n\n";
            $text .= "📝 الوصف: {$game['description']}\n";
            $text .= "⭐ التقييم: {$game['rating']}/5\n";
            $text .= "📦 الحجم: {$game['size']}\n";
            $text .= "🏷️ الفئة: {$game['category']}\n\n";
            $text .= "يمكنك تحميل اللعبة من الرابط أدناه:";

            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '📥 تحميل اللعبة', 'url' => $game['download_link']]],
                [['text' => '• رجوع •', 'callback_data' => 'games_' . $game['category_id']]]
            ]);
        }

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard,
            'parse_mode' => 'Markdown'
        ]);
    }

    /**
     * Show app details
     */
    public function showAppDetails($chatId, $messageId, $appId)
    {
        $app = $this->getAppById($appId);
        
        if (!$app) {
            $text = "❌ التطبيق غير موجود";
            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '• رجوع •', 'callback_data' => 'apps_menu']]
            ]);
        } else {
            $text = "📱 **{$app['name']}**\n\n";
            $text .= "📝 الوصف: {$app['description']}\n";
            $text .= "⭐ التقييم: {$app['rating']}/5\n";
            $text .= "📦 الحجم: {$app['size']}\n";
            $text .= "🏷️ الفئة: {$app['category']}\n\n";
            $text .= "يمكنك تحميل التطبيق من الرابط أدناه:";

            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '📥 تحميل التطبيق', 'url' => $app['download_link']]],
                [['text' => '• رجوع •', 'callback_data' => 'apps_' . $app['category_id']]]
            ]);
        }

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard,
            'parse_mode' => 'Markdown'
        ]);
    }

    /**
     * Get games by category
     */
    private function getGamesByCategory($category)
    {
        // This would typically come from a database or configuration file
        // For now, returning sample data
        $allGames = [
            'action' => [
                ['id' => 'pubg', 'name' => 'PUBG Mobile', 'description' => 'لعبة باتل رويال شهيرة', 'rating' => '4.5', 'size' => '2.5 GB', 'category' => 'حركة', 'category_id' => 'action', 'download_link' => 'https://example.com/pubg'],
                ['id' => 'cod', 'name' => 'Call of Duty Mobile', 'description' => 'لعبة إطلاق نار متعددة اللاعبين', 'rating' => '4.3', 'size' => '3.2 GB', 'category' => 'حركة', 'category_id' => 'action', 'download_link' => 'https://example.com/cod']
            ],
            'puzzle' => [
                ['id' => 'candy', 'name' => 'Candy Crush Saga', 'description' => 'لعبة ألغاز ممتعة', 'rating' => '4.2', 'size' => '150 MB', 'category' => 'ألغاز', 'category_id' => 'puzzle', 'download_link' => 'https://example.com/candy']
            ]
        ];

        return $allGames[$category] ?? [];
    }

    /**
     * Get apps by category
     */
    private function getAppsByCategory($category)
    {
        // This would typically come from a database or configuration file
        $allApps = [
            'social' => [
                ['id' => 'whatsapp', 'name' => 'WhatsApp', 'description' => 'تطبيق مراسلة فورية', 'rating' => '4.6', 'size' => '120 MB', 'category' => 'تواصل', 'category_id' => 'social', 'download_link' => 'https://example.com/whatsapp'],
                ['id' => 'telegram', 'name' => 'Telegram', 'description' => 'تطبيق مراسلة آمن', 'rating' => '4.5', 'size' => '80 MB', 'category' => 'تواصل', 'category_id' => 'social', 'download_link' => 'https://example.com/telegram']
            ],
            'music' => [
                ['id' => 'spotify', 'name' => 'Spotify', 'description' => 'تطبيق موسيقى وبودكاست', 'rating' => '4.4', 'size' => '100 MB', 'category' => 'موسيقى', 'category_id' => 'music', 'download_link' => 'https://example.com/spotify']
            ]
        ];

        return $allApps[$category] ?? [];
    }

    /**
     * Get game by ID
     */
    private function getGameById($gameId)
    {
        $allGames = array_merge(
            $this->getGamesByCategory('action'),
            $this->getGamesByCategory('puzzle')
            // Add other categories as needed
        );

        foreach ($allGames as $game) {
            if ($game['id'] === $gameId) {
                return $game;
            }
        }

        return null;
    }

    /**
     * Get app by ID
     */
    private function getAppById($appId)
    {
        $allApps = array_merge(
            $this->getAppsByCategory('social'),
            $this->getAppsByCategory('music')
            // Add other categories as needed
        );

        foreach ($allApps as $app) {
            if ($app['id'] === $appId) {
                return $app;
            }
        }

        return null;
    }

    /**
     * Handle text-based game/app requests
     */
    public function handleTextRequest($chatId, $text)
    {
        // Define app links for text-based requests
        $appLinks = [
            'واتساب' => 'https://play.google.com/store/apps/details?id=com.whatsapp',
            'تيليجرام' => 'https://play.google.com/store/apps/details?id=org.telegram.messenger',
            'انستقرام' => 'https://play.google.com/store/apps/details?id=com.instagram.android',
            'فيسبوك' => 'https://play.google.com/store/apps/details?id=com.facebook.katana',
            'يوتيوب' => 'https://play.google.com/store/apps/details?id=com.google.android.youtube',
            'تيك توك' => 'https://play.google.com/store/apps/details?id=com.zhiliaoapp.musically',
            'سناب شات' => 'https://play.google.com/store/apps/details?id=com.snapchat.android',
            'تويتر' => 'https://play.google.com/store/apps/details?id=com.twitter.android'
        ];

        if (isset($appLinks[$text])) {
            $response = "لقد طلبت 👈🏻 $text! يمكنك تحميله من هنا ⬇️:\n\n" . $appLinks[$text];
            
            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '📥 تحميل التطبيق', 'url' => $appLinks[$text]]],
                [['text' => '🎮 القائمة الرئيسية', 'callback_data' => 'main_menu']]
            ]);

            return $this->api->sendMessage($chatId, $response, [
                'reply_markup' => $keyboard,
                'disable_web_page_preview' => true
            ]);
        }

        return false;
    }

    /**
     * Show offline games
     */
    public function showOfflineGames($chatId, $messageId)
    {
        $text = "🎮 ألعاب بدون إنترنت\n\n";
        $text .= "قريباً سيتم إضافة مجموعة من الألعاب التي تعمل بدون إنترنت!";

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '• رجوع •', 'callback_data' => 'main_menu']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }

    /**
     * Show paid services
     */
    public function showPaidServices($chatId, $messageId)
    {
        $text = "💰 خدمات مدفوعة\n\n";
        $text .= "للحصول على خدمات مدفوعة أو تطبيقات مميزة، يرجى التواصل مع المطور.";

        $keyboard = TelegramAPI::createInlineKeyboard([
            [['text' => '📞 تواصل مع المطور', 'url' => 't.me/GoogleYooz']],
            [['text' => '• رجوع •', 'callback_data' => 'main_menu']]
        ]);

        return $this->api->editMessageText($chatId, $messageId, $text, [
            'reply_markup' => $keyboard
        ]);
    }
}
