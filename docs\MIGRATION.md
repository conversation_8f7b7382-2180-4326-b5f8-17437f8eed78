# Migration Guide

This guide helps you migrate from the old monolithic Telegram bot structure to the new modular architecture.

## Table of Contents

- [Overview](#overview)
- [Pre-Migration Checklist](#pre-migration-checklist)
- [Data Migration](#data-migration)
- [Configuration Migration](#configuration-migration)
- [Code Migration](#code-migration)
- [Testing Migration](#testing-migration)
- [Post-Migration Tasks](#post-migration-tasks)
- [Rollback Plan](#rollback-plan)

## Overview

### What's Changed

The new modular architecture provides:

- **Separation of Concerns**: Controllers, Services, and Middleware
- **Better Security**: Input validation and security middleware
- **Improved Maintainability**: Clean, organized code structure
- **Enhanced Features**: Better admin panel, broadcasting, and user management
- **Professional Standards**: PSR-12 compliant code

### Migration Strategy

1. **Backup Current System**: Complete backup of existing bot
2. **Parallel Setup**: Set up new system alongside old one
3. **Data Migration**: Transfer existing data to new structure
4. **Testing**: Thorough testing of new system
5. **Cutover**: Switch webhook to new system
6. **Cleanup**: Remove old system after verification

## Pre-Migration Checklist

### 1. Backup Current System

```bash
# Create backup directory
mkdir telegram-bot-backup-$(date +%Y%m%d)

# Backup all files
cp -r /path/to/old/bot/* telegram-bot-backup-$(date +%Y%m%d)/

# Backup database (if using database)
mysqldump -u username -p database_name > backup.sql
```

### 2. Document Current Configuration

Create a migration checklist:

```
□ Bot Token: _______________
□ Admin User ID: ___________
□ Channel Username: ________
□ Current webhook URL: _____
□ Number of users: _________
□ Number of admins: ________
□ Custom messages: _________
□ Special configurations: ___
```

### 3. Identify Custom Features

List any custom features in your old bot:
- Custom commands
- Special message handlers
- Additional integrations
- Custom admin functions

## Data Migration

### 1. Extract Data from Old Bot

#### From File-Based Storage

If your old bot uses files similar to the new structure:

```php
// migration/extract_data.php
<?php

// Old bot paths
$oldBasePath = '/path/to/old/bot/';
$oldMembersFile = $oldBasePath . 'members.txt';
$oldAdminsFile = $oldBasePath . 'admins.txt';
$oldBannedFile = $oldBasePath . 'banned.txt';

// Extract data
$members = file_exists($oldMembersFile) ? file($oldMembersFile, FILE_IGNORE_NEW_LINES) : [];
$admins = file_exists($oldAdminsFile) ? file($oldAdminsFile, FILE_IGNORE_NEW_LINES) : [];
$banned = file_exists($oldBannedFile) ? file($oldBannedFile, FILE_IGNORE_NEW_LINES) : [];

// Save extracted data
$extractedData = [
    'members' => array_filter($members),
    'admins' => array_filter($admins),
    'banned' => array_filter($banned),
    'extraction_date' => date('Y-m-d H:i:s')
];

file_put_contents('extracted_data.json', json_encode($extractedData, JSON_PRETTY_PRINT));
echo "Data extracted successfully\n";
```

#### From Database

If your old bot uses a database:

```php
// migration/extract_from_db.php
<?php

$pdo = new PDO('mysql:host=localhost;dbname=old_bot_db', $username, $password);

// Extract users
$stmt = $pdo->query("SELECT user_id FROM users WHERE active = 1");
$members = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Extract admins
$stmt = $pdo->query("SELECT user_id FROM users WHERE is_admin = 1");
$admins = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Extract banned users
$stmt = $pdo->query("SELECT user_id FROM users WHERE is_banned = 1");
$banned = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Save data
$extractedData = [
    'members' => $members,
    'admins' => $admins,
    'banned' => $banned,
    'extraction_date' => date('Y-m-d H:i:s')
];

file_put_contents('extracted_data.json', json_encode($extractedData, JSON_PRETTY_PRINT));
```

### 2. Import Data to New System

```php
// migration/import_data.php
<?php

require_once '../config/config.php';
require_once '../src/Services/FileService.php';

use TelegramBot\Services\FileService;

// Load extracted data
$extractedData = json_decode(file_get_contents('extracted_data.json'), true);

// Initialize new system
$config = require '../config/config.php';
$fileService = new FileService($config);

// Import members
foreach ($extractedData['members'] as $userId) {
    if (!empty($userId) && is_numeric($userId)) {
        $fileService->addMember($userId);
        echo "Imported member: $userId\n";
    }
}

// Import admins
foreach ($extractedData['admins'] as $userId) {
    if (!empty($userId) && is_numeric($userId)) {
        $fileService->addAdmin($userId);
        echo "Imported admin: $userId\n";
    }
}

// Import banned users
foreach ($extractedData['banned'] as $userId) {
    if (!empty($userId) && is_numeric($userId)) {
        $fileService->banUser($userId);
        echo "Imported banned user: $userId\n";
    }
}

echo "Data import completed\n";
```

### 3. Migrate Custom Messages

```php
// migration/migrate_messages.php
<?php

require_once '../config/config.php';
require_once '../src/Services/FileService.php';

use TelegramBot\Services\FileService;

$config = require '../config/config.php';
$fileService = new FileService($config);

// Migrate welcome message
$oldWelcomeMessage = file_get_contents('/path/to/old/welcome.txt');
if ($oldWelcomeMessage) {
    $fileService->setWelcomeMessage($oldWelcomeMessage);
    echo "Welcome message migrated\n";
}

// Migrate auto-reply message
$oldAutoReply = file_get_contents('/path/to/old/auto_reply.txt');
if ($oldAutoReply) {
    $fileService->setAutoReplyMessage($oldAutoReply);
    echo "Auto-reply message migrated\n";
}
```

## Configuration Migration

### 1. Map Old Configuration to New

Create a mapping of old configuration to new:

```php
// migration/config_mapping.php
<?php

// Old configuration values
$oldConfig = [
    'bot_token' => 'your_old_token',
    'admin_id' => 123456789,
    'channel' => '@oldchannel',
    'welcome_msg' => 'Old welcome message',
    // ... other old config values
];

// Map to new configuration format
$newConfigTemplate = '<?php
// Bot Configuration
define(\'BOT_TOKEN\', \'' . $oldConfig['bot_token'] . '\');
define(\'MAIN_ADMIN_ID\', ' . $oldConfig['admin_id'] . ');
define(\'CHANNEL_USERNAME\', \'' . $oldConfig['channel'] . '\');

// Feature Flags
define(\'ENABLE_SUBSCRIPTION_CHECK\', true);
define(\'ENABLE_ADMIN_NOTIFICATIONS\', true);
define(\'ENABLE_MESSAGE_LOGGING\', true);

// ... rest of configuration
';

file_put_contents('../config/config.php', $newConfigTemplate);
echo "Configuration migrated\n";
```

### 2. Update Settings File

```php
// migration/migrate_settings.php
<?php

require_once '../config/config.php';
require_once '../src/Services/FileService.php';

use TelegramBot\Services\FileService;

$config = require '../config/config.php';
$fileService = new FileService($config);

// Migrate settings from old system
$oldSettings = [
    'notifications_enabled' => true,
    'subscription_check' => true,
    'auto_reply_enabled' => true,
    'media_restrictions' => [
        'photos' => false,
        'videos' => false,
        'documents' => false,
        'stickers' => false,
        'voice' => false,
        'audio' => false,
        'forwards' => false
    ]
];

$fileService->writeJson($config['files']['settings'], $oldSettings);
echo "Settings migrated\n";
```

## Code Migration

### 1. Migrate Custom Commands

If you have custom commands in the old bot, integrate them into the new system:

```php
// In src/Controllers/UserController.php

private function handleCustomCommands($message)
{
    $text = $message['text'] ?? '';
    $userId = $message['from']['id'];
    $chatId = $message['chat']['id'];

    switch ($text) {
        case '/custom_command':
            // Your custom command logic here
            return $this->api->sendMessage($chatId, 'Custom command response');
            
        case '/another_command':
            // Another custom command
            return $this->handleAnotherCommand($message);
            
        default:
            return false;
    }
}

// Add to handleUserMessage method
public function handleUserMessage($message)
{
    // ... existing code ...
    
    // Check for custom commands
    if ($this->handleCustomCommands($message)) {
        return true;
    }
    
    // ... rest of existing code ...
}
```

### 2. Migrate Custom Admin Functions

```php
// In src/Controllers/AdminController.php

public function handleCustomAdminFunction($chatId, $messageId, $action)
{
    switch ($action) {
        case 'custom_admin_action':
            // Your custom admin logic here
            $text = "Custom admin function result";
            
            $keyboard = TelegramAPI::createInlineKeyboard([
                [['text' => '• رجوع •', 'callback_data' => 'admin_panel']]
            ]);
            
            return $this->api->editMessageText($chatId, $messageId, $text, [
                'reply_markup' => $keyboard
            ]);
            
        default:
            return false;
    }
}
```

### 3. Migrate Database Integration (if needed)

If you want to add database support:

```php
// src/Services/DatabaseService.php
<?php

namespace TelegramBot\Services;

class DatabaseService
{
    private $pdo;
    
    public function __construct($config)
    {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
        $this->pdo = new PDO($dsn, $config['username'], $config['password']);
        $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    public function addUser($userId, $userData)
    {
        $stmt = $this->pdo->prepare("INSERT IGNORE INTO users (user_id, first_name, username, created_at) VALUES (?, ?, ?, NOW())");
        return $stmt->execute([$userId, $userData['first_name'], $userData['username']]);
    }
    
    // ... other database methods
}
```

## Testing Migration

### 1. Create Test Script

```php
// migration/test_migration.php
<?php

require_once '../config/config.php';
require_once '../src/Bot.php';

use TelegramBot\Bot;

$bot = new Bot();

// Test 1: Bot initialization
echo "Testing bot initialization... ";
$botInfo = $bot->getBotInfo();
if ($botInfo) {
    echo "✅ PASSED\n";
} else {
    echo "❌ FAILED\n";
}

// Test 2: File service
echo "Testing file service... ";
$stats = $bot->getStatistics();
if ($stats) {
    echo "✅ PASSED\n";
} else {
    echo "❌ FAILED\n";
}

// Test 3: Admin functionality
echo "Testing admin functionality... ";
// Add your admin tests here

// Test 4: User data integrity
echo "Testing user data integrity... ";
// Verify migrated data

echo "Migration testing completed\n";
```

### 2. Verify Data Integrity

```php
// migration/verify_data.php
<?php

require_once '../config/config.php';
require_once '../src/Services/FileService.php';

use TelegramBot\Services\FileService;

$config = require '../config/config.php';
$fileService = new FileService($config);

// Load original data
$originalData = json_decode(file_get_contents('extracted_data.json'), true);

// Verify members
$migratedMembers = $fileService->getMembers();
$membersDiff = array_diff($originalData['members'], $migratedMembers);

echo "Original members: " . count($originalData['members']) . "\n";
echo "Migrated members: " . count($migratedMembers) . "\n";
echo "Missing members: " . count($membersDiff) . "\n";

if (!empty($membersDiff)) {
    echo "Missing: " . implode(', ', $membersDiff) . "\n";
}

// Verify admins
$migratedAdmins = $fileService->getAdmins();
$adminsDiff = array_diff($originalData['admins'], $migratedAdmins);

echo "Original admins: " . count($originalData['admins']) . "\n";
echo "Migrated admins: " . count($migratedAdmins) . "\n";
echo "Missing admins: " . count($adminsDiff) . "\n";

// Verify banned users
$migratedBanned = $fileService->getBannedUsers();
$bannedDiff = array_diff($originalData['banned'], $migratedBanned);

echo "Original banned: " . count($originalData['banned']) . "\n";
echo "Migrated banned: " . count($migratedBanned) . "\n";
echo "Missing banned: " . count($bannedDiff) . "\n";
```

## Post-Migration Tasks

### 1. Update Webhook

```bash
# Delete old webhook
curl -X POST "https://api.telegram.org/bot<OLD_TOKEN>/deleteWebhook"

# Set new webhook
curl -X POST "https://api.telegram.org/bot<NEW_TOKEN>/setWebhook" \
     -d "url=https://yourdomain.com/new-bot/public/webhook.php"
```

### 2. Monitor New System

```php
// migration/monitor.php
<?php

require_once '../config/config.php';
require_once '../src/Bot.php';

use TelegramBot\Bot;

$bot = new Bot();

// Monitor for 24 hours
$startTime = time();
$monitorDuration = 24 * 60 * 60; // 24 hours

while (time() - $startTime < $monitorDuration) {
    // Check system health
    $stats = $bot->getStatistics();
    
    if (!$stats) {
        echo "[" . date('Y-m-d H:i:s') . "] ERROR: Unable to get statistics\n";
    } else {
        echo "[" . date('Y-m-d H:i:s') . "] OK: System running normally\n";
    }
    
    // Check log files for errors
    $logFile = LOGS_PATH . date('Y-m-d') . '.log';
    if (file_exists($logFile)) {
        $logContent = file_get_contents($logFile);
        $errorCount = substr_count($logContent, '[error]');
        
        if ($errorCount > 0) {
            echo "[" . date('Y-m-d H:i:s') . "] WARNING: $errorCount errors in log\n";
        }
    }
    
    sleep(300); // Check every 5 minutes
}
```

### 3. Performance Comparison

```php
// migration/performance_check.php
<?php

// Compare performance metrics
$oldStats = [
    'response_time' => 0.5, // seconds
    'memory_usage' => 32, // MB
    'error_rate' => 0.1 // %
];

require_once '../config/config.php';
require_once '../src/Bot.php';

use TelegramBot\Bot;

$bot = new Bot();

// Measure new system performance
$startTime = microtime(true);
$startMemory = memory_get_usage(true);

// Simulate bot operations
$stats = $bot->getStatistics();
$botInfo = $bot->getBotInfo();

$endTime = microtime(true);
$endMemory = memory_get_usage(true);

$newStats = [
    'response_time' => $endTime - $startTime,
    'memory_usage' => ($endMemory - $startMemory) / 1024 / 1024,
    'error_rate' => 0 // Calculate based on logs
];

echo "Performance Comparison:\n";
echo "Response Time: {$oldStats['response_time']}s -> {$newStats['response_time']}s\n";
echo "Memory Usage: {$oldStats['memory_usage']}MB -> {$newStats['memory_usage']}MB\n";
echo "Error Rate: {$oldStats['error_rate']}% -> {$newStats['error_rate']}%\n";
```

## Rollback Plan

### 1. Prepare Rollback Script

```bash
#!/bin/bash
# rollback.sh

echo "Starting rollback process..."

# Stop new system
echo "Stopping new system..."
# Add commands to stop new system

# Restore old webhook
echo "Restoring old webhook..."
curl -X POST "https://api.telegram.org/bot<OLD_TOKEN>/setWebhook" \
     -d "url=https://yourdomain.com/old-bot/webhook.php"

# Restore old files
echo "Restoring old files..."
cp -r telegram-bot-backup-*/  /path/to/old/bot/

# Restart old system
echo "Restarting old system..."
# Add commands to restart old system

echo "Rollback completed"
```

### 2. Rollback Triggers

Define when to rollback:
- Error rate > 5%
- Response time > 2x old system
- Critical functionality not working
- Data loss detected

### 3. Communication Plan

Prepare messages for users:
- Maintenance notification
- Rollback notification
- Resolution update

## Migration Checklist

```
Pre-Migration:
□ Backup old system
□ Document current configuration
□ Identify custom features
□ Prepare migration scripts

Data Migration:
□ Extract user data
□ Extract admin data
□ Extract banned users
□ Migrate custom messages
□ Verify data integrity

Configuration Migration:
□ Update bot token
□ Set admin user ID
□ Configure channels
□ Migrate settings
□ Test configuration

Code Migration:
□ Migrate custom commands
□ Migrate admin functions
□ Update integrations
□ Test custom features

Testing:
□ Unit tests pass
□ Integration tests pass
□ Performance tests pass
□ User acceptance testing

Deployment:
□ Update webhook
□ Monitor system health
□ Check error logs
□ Verify functionality

Post-Migration:
□ Performance monitoring
□ User feedback collection
□ Documentation updates
□ Team training
```

## Common Migration Issues

### Issue: Data Loss
**Solution**: Always backup before migration and verify data integrity

### Issue: Webhook Not Working
**Solution**: Check SSL certificate and webhook URL accessibility

### Issue: Performance Degradation
**Solution**: Optimize PHP settings and enable OPcache

### Issue: Custom Features Not Working
**Solution**: Review and update custom code for new architecture

### Issue: User Complaints
**Solution**: Communicate changes clearly and provide support

Remember: Take your time with migration and test thoroughly before going live!
