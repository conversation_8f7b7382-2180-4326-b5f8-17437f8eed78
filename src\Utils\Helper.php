<?php

namespace TelegramBot\Utils;

/**
 * Helper Utility Class
 * 
 * Provides common utility functions used throughout the bot.
 */
class Helper
{
    /**
     * Format file size in human readable format
     */
    public static function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Format time duration in human readable format
     */
    public static function formatDuration($seconds)
    {
        if ($seconds < 60) {
            return $seconds . ' seconds';
        } elseif ($seconds < 3600) {
            return round($seconds / 60) . ' minutes';
        } elseif ($seconds < 86400) {
            return round($seconds / 3600) . ' hours';
        } else {
            return round($seconds / 86400) . ' days';
        }
    }

    /**
     * Format number with thousands separator
     */
    public static function formatNumber($number)
    {
        return number_format($number);
    }

    /**
     * Truncate text to specified length
     */
    public static function truncateText($text, $length = 100, $suffix = '...')
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length) . $suffix;
    }

    /**
     * Clean and format username
     */
    public static function formatUsername($username)
    {
        if (empty($username)) {
            return '';
        }
        
        // Remove @ if present
        $username = ltrim($username, '@');
        
        // Add @ back
        return '@' . $username;
    }

    /**
     * Generate random string
     */
    public static function generateRandomString($length = 10)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $randomString;
    }

    /**
     * Generate random numeric code
     */
    public static function generateNumericCode($length = 6)
    {
        $min = pow(10, $length - 1);
        $max = pow(10, $length) - 1;
        
        return rand($min, $max);
    }

    /**
     * Validate email address
     */
    public static function isValidEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate URL
     */
    public static function isValidUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Extract URLs from text
     */
    public static function extractUrls($text)
    {
        $pattern = '/https?:\/\/[^\s]+/i';
        preg_match_all($pattern, $text, $matches);
        return $matches[0];
    }

    /**
     * Extract mentions from text
     */
    public static function extractMentions($text)
    {
        $pattern = '/@([a-zA-Z0-9_]+)/';
        preg_match_all($pattern, $text, $matches);
        return $matches[1];
    }

    /**
     * Extract hashtags from text
     */
    public static function extractHashtags($text)
    {
        $pattern = '/#([a-zA-Z0-9_]+)/';
        preg_match_all($pattern, $text, $matches);
        return $matches[1];
    }

    /**
     * Convert Arabic numerals to English
     */
    public static function arabicToEnglishNumbers($text)
    {
        $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        return str_replace($arabicNumbers, $englishNumbers, $text);
    }

    /**
     * Convert English numerals to Arabic
     */
    public static function englishToArabicNumbers($text)
    {
        $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        
        return str_replace($englishNumbers, $arabicNumbers, $text);
    }

    /**
     * Escape Markdown special characters
     */
    public static function escapeMarkdown($text)
    {
        $specialChars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!'];
        
        foreach ($specialChars as $char) {
            $text = str_replace($char, '\\' . $char, $text);
        }
        
        return $text;
    }

    /**
     * Escape HTML special characters
     */
    public static function escapeHtml($text)
    {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Create user mention link
     */
    public static function createUserMention($userId, $name)
    {
        return "[{$name}](tg://user?id={$userId})";
    }

    /**
     * Create channel/group link
     */
    public static function createChannelLink($username, $text = null)
    {
        $username = ltrim($username, '@');
        $text = $text ?: "@{$username}";
        
        return "[{$text}](https://t.me/{$username})";
    }

    /**
     * Parse command and arguments
     */
    public static function parseCommand($text)
    {
        $parts = explode(' ', trim($text), 2);
        
        return [
            'command' => $parts[0],
            'arguments' => isset($parts[1]) ? $parts[1] : ''
        ];
    }

    /**
     * Check if text is a command
     */
    public static function isCommand($text)
    {
        return strpos(trim($text), '/') === 0;
    }

    /**
     * Get file extension from filename
     */
    public static function getFileExtension($filename)
    {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    }

    /**
     * Get MIME type from file extension
     */
    public static function getMimeType($extension)
    {
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'mp3' => 'audio/mpeg',
            'mp4' => 'video/mp4',
            'avi' => 'video/x-msvideo',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            'txt' => 'text/plain'
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    /**
     * Check if file type is image
     */
    public static function isImageFile($extension)
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        return in_array(strtolower($extension), $imageExtensions);
    }

    /**
     * Check if file type is video
     */
    public static function isVideoFile($extension)
    {
        $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
        return in_array(strtolower($extension), $videoExtensions);
    }

    /**
     * Check if file type is audio
     */
    public static function isAudioFile($extension)
    {
        $audioExtensions = ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a'];
        return in_array(strtolower($extension), $audioExtensions);
    }

    /**
     * Generate UUID v4
     */
    public static function generateUuid()
    {
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
        
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /**
     * Calculate time ago
     */
    public static function timeAgo($timestamp)
    {
        $time = time() - $timestamp;
        
        if ($time < 60) {
            return 'just now';
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($time < 2592000) {
            $days = floor($time / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } else {
            return date('Y-m-d', $timestamp);
        }
    }

    /**
     * Convert timestamp to readable date
     */
    public static function formatDate($timestamp, $format = 'Y-m-d H:i:s')
    {
        return date($format, $timestamp);
    }

    /**
     * Check if string contains only Arabic characters
     */
    public static function isArabicText($text)
    {
        return preg_match('/^[\p{Arabic}\s\p{P}]+$/u', $text);
    }

    /**
     * Check if string contains only English characters
     */
    public static function isEnglishText($text)
    {
        return preg_match('/^[a-zA-Z\s\p{P}]+$/u', $text);
    }

    /**
     * Detect text language (simple detection)
     */
    public static function detectLanguage($text)
    {
        if (self::isArabicText($text)) {
            return 'ar';
        } elseif (self::isEnglishText($text)) {
            return 'en';
        } else {
            return 'mixed';
        }
    }

    /**
     * Clean text for storage
     */
    public static function cleanText($text)
    {
        // Remove null bytes
        $text = str_replace("\0", '', $text);
        
        // Normalize whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        // Trim
        $text = trim($text);
        
        return $text;
    }

    /**
     * Check if IP address is valid
     */
    public static function isValidIp($ip)
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * Get client IP address
     */
    public static function getClientIp()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (self::isValidIp($ip)) {
                    return $ip;
                }
            }
        }
        
        return 'unknown';
    }

    /**
     * Create safe filename
     */
    public static function createSafeFilename($filename)
    {
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // Remove multiple underscores
        $filename = preg_replace('/_+/', '_', $filename);
        
        // Trim underscores
        $filename = trim($filename, '_');
        
        // Ensure it's not empty
        if (empty($filename)) {
            $filename = 'file_' . time();
        }
        
        return $filename;
    }

    /**
     * Array to CSV string
     */
    public static function arrayToCsv($array, $delimiter = ',')
    {
        $output = fopen('php://temp', 'r+');
        
        foreach ($array as $row) {
            fputcsv($output, $row, $delimiter);
        }
        
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return $csv;
    }

    /**
     * CSV string to array
     */
    public static function csvToArray($csv, $delimiter = ',')
    {
        $lines = explode("\n", trim($csv));
        $array = [];
        
        foreach ($lines as $line) {
            if (!empty($line)) {
                $array[] = str_getcsv($line, $delimiter);
            }
        }
        
        return $array;
    }

    /**
     * Check if array is associative
     */
    public static function isAssociativeArray($array)
    {
        if (!is_array($array)) {
            return false;
        }
        
        return array_keys($array) !== range(0, count($array) - 1);
    }

    /**
     * Deep merge arrays
     */
    public static function arrayMergeDeep($array1, $array2)
    {
        $merged = $array1;
        
        foreach ($array2 as $key => $value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                $merged[$key] = self::arrayMergeDeep($merged[$key], $value);
            } else {
                $merged[$key] = $value;
            }
        }
        
        return $merged;
    }
}
