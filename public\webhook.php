<?php
/**
 * Telegram Bot Webhook Endpoint
 * 
 * This is the main entry point for the Telegram bot webhook.
 * All incoming updates from Telegram will be processed through this file.
 */

// Set error reporting for production
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

// Set content type
header('Content-Type: application/json');

// Include required files
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/TelegramAPI.php';
require_once __DIR__ . '/../src/Services/FileService.php';
require_once __DIR__ . '/../src/Services/UserService.php';
require_once __DIR__ . '/../src/Services/MessageService.php';
require_once __DIR__ . '/../src/Services/ValidationService.php';
require_once __DIR__ . '/../src/Controllers/AdminController.php';
require_once __DIR__ . '/../src/Controllers/UserController.php';
require_once __DIR__ . '/../src/Controllers/BroadcastController.php';
require_once __DIR__ . '/../src/Controllers/GameController.php';
require_once __DIR__ . '/../src/Router.php';
require_once __DIR__ . '/../src/Bot.php';

try {
    // Initialize bot
    $bot = new TelegramBot\Bot();
    
    // Handle the webhook
    $result = $bot->handleWebhook();
    
    // Send success response
    if ($result) {
        http_response_code(200);
        echo json_encode(['status' => 'ok']);
    } else {
        http_response_code(200); // Still return 200 to Telegram
        echo json_encode(['status' => 'processed']);
    }

} catch (Exception $e) {
    // Log error
    error_log('Webhook Error: ' . $e->getMessage());
    
    // Send error response
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
