# Troubleshooting Guide

This guide helps you resolve common issues with the VEVoGamez Telegram Bot.

## Table of Contents

- [Constant Redefinition Warnings](#constant-redefinition-warnings)
- [Configuration Issues](#configuration-issues)
- [Permission Problems](#permission-problems)
- [Webhook Issues](#webhook-issues)
- [Performance Issues](#performance-issues)
- [Common Error Messages](#common-error-messages)

## Constant Redefinition Warnings

### Problem
You see warnings like:
```
Warning: Constant BOT_TOKEN already defined in /path/to/config/config.php on line 10
```

### Causes
1. Configuration file included multiple times
2. Old cached files
3. OPcache issues
4. Multiple entry points loading config

### Solutions

#### Solution 1: Use Updated Configuration
Make sure you're using the updated `config.php` with include guards:

```php
// At the top of config.php
if (defined('BOT_CONFIG_LOADED')) {
    return $config ?? [];
}
define('BOT_CONFIG_LOADED', true);

// Then all constants with guards
if (!defined('BOT_TOKEN')) {
    define('BOT_TOKEN', 'your_token_here');
}
```

#### Solution 2: Clear PHP OPcache
```php
// Add this to your script or run separately
if (function_exists('opcache_reset')) {
    opcache_reset();
}
```

#### Solution 3: Use require_once
Instead of `require` or `include`, use `require_once`:
```php
require_once __DIR__ . '/config/config.php';
```

#### Solution 4: Run Fix Script
```bash
php scripts/fix_constants.php
```

#### Solution 5: Restart Web Server
```bash
# Apache
sudo systemctl restart apache2

# Nginx
sudo systemctl restart nginx

# PHP-FPM
sudo systemctl restart php8.1-fpm
```

### Testing the Fix
Run the configuration test:
```bash
php tests/test_config.php
```

## Configuration Issues

### Bot Token Invalid
**Error**: `Invalid bot token format`

**Solution**:
1. Get a new token from [@BotFather](https://t.me/BotFather)
2. Token format should be: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`
3. Update `config.php` with the correct token

### Admin ID Not Working
**Error**: Admin commands not working

**Solution**:
1. Get your user ID from [@userinfobot](https://t.me/userinfobot)
2. Update `MAIN_ADMIN_ID` in `config.php`
3. Make sure it's a number, not a string

### File Paths Wrong
**Error**: `Failed to create directory` or `Permission denied`

**Solution**:
1. Check if paths in `config.php` are correct
2. Ensure storage directory exists and is writable
3. Run installation script: `php scripts/install.php`

## Permission Problems

### Storage Directory Not Writable
**Error**: `Permission denied` when writing files

**Solution**:
```bash
# Set correct permissions
chmod -R 755 storage/
chown -R www-data:www-data storage/

# For specific directories
chmod 755 storage/admin/
chmod 755 storage/users/
chmod 755 storage/logs/
```

### Config File Not Readable
**Error**: `Failed to load configuration`

**Solution**:
```bash
chmod 644 config/config.php
chown www-data:www-data config/config.php
```

### Web Server User Issues
Find your web server user:
```bash
# Check Apache user
ps aux | grep apache

# Check Nginx user
ps aux | grep nginx

# Set ownership accordingly
chown -R [webserver-user]:[webserver-group] /path/to/bot/
```

## Webhook Issues

### Webhook Not Set
**Error**: Bot not responding to messages

**Solution**:
1. Access `public/setup.php` in your browser
2. Click "Set Webhook"
3. Verify webhook URL is correct and accessible

### SSL Certificate Issues
**Error**: `SSL certificate verify failed`

**Solution**:
1. Ensure your domain has a valid SSL certificate
2. Test SSL: `curl -I https://yourdomain.com`
3. Use Let's Encrypt if needed:
   ```bash
   sudo certbot --nginx -d yourdomain.com
   ```

### Webhook URL Not Accessible
**Error**: `Webhook URL is not accessible`

**Solution**:
1. Test webhook URL: `curl https://yourdomain.com/path/to/webhook.php`
2. Check server logs for errors
3. Verify file permissions on `webhook.php`

### Wrong Webhook URL
**Error**: Bot responds to old commands

**Solution**:
1. Delete old webhook: `curl -X POST "https://api.telegram.org/bot<TOKEN>/deleteWebhook"`
2. Set new webhook through setup page
3. Verify with: `curl "https://api.telegram.org/bot<TOKEN>/getWebhookInfo"`

## Performance Issues

### Slow Response Times
**Symptoms**: Bot takes long to respond

**Solutions**:
1. Enable PHP OPcache:
   ```ini
   ; php.ini
   opcache.enable=1
   opcache.memory_consumption=128
   opcache.max_accelerated_files=4000
   ```

2. Optimize file operations:
   ```bash
   # Use SSD storage if possible
   # Increase PHP memory limit
   memory_limit = 256M
   ```

3. Check server resources:
   ```bash
   top
   df -h
   free -m
   ```

### High Memory Usage
**Symptoms**: Out of memory errors

**Solutions**:
1. Increase PHP memory limit:
   ```ini
   memory_limit = 512M
   ```

2. Optimize code:
   - Use `unset()` for large variables
   - Avoid loading large files into memory
   - Use file streaming for large operations

### Database/File Locks
**Symptoms**: Bot becomes unresponsive

**Solutions**:
1. Check for file locks:
   ```bash
   lsof | grep /path/to/storage/
   ```

2. Restart PHP-FPM:
   ```bash
   sudo systemctl restart php8.1-fpm
   ```

## Common Error Messages

### "Undefined array key 'files'"
**Error**: `Warning: Undefined array key "files" in FileService.php`

**Solution**:
1. Ensure configuration is loaded properly:
   ```php
   $config = require __DIR__ . '/config/config.php';
   $fileService = new FileService($config);
   ```
2. Check if config.php returns the $config array
3. Verify the config file structure includes 'files' array
4. Use the updated FileService with fallback support

### "Class not found"
**Error**: `Fatal error: Class 'TelegramBot\Bot' not found`

**Solution**:
1. Check file paths in includes
2. Verify namespace declarations
3. Ensure all required files are present

### "Call to undefined function"
**Error**: `Fatal error: Call to undefined function curl_init()`

**Solution**:
```bash
# Install cURL extension
sudo apt install php-curl

# Restart web server
sudo systemctl restart apache2
```

### "JSON decode error"
**Error**: `JSON decode failed`

**Solution**:
1. Check JSON file syntax
2. Verify file permissions
3. Look for BOM or encoding issues

### "Rate limit exceeded"
**Error**: Bot stops responding

**Solution**:
1. Check Telegram API limits
2. Implement proper rate limiting
3. Add delays between requests

### "Webhook timeout"
**Error**: `Request timeout`

**Solution**:
1. Optimize webhook response time
2. Increase server timeout settings
3. Use background processing for heavy tasks

## Debugging Steps

### 1. Enable Debug Mode
```php
// In config.php
define('DEBUG_MODE', true);
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### 2. Check Logs
```bash
# PHP error log
tail -f /var/log/php/error.log

# Bot logs
tail -f storage/logs/$(date +%Y-%m-%d).log

# Web server logs
tail -f /var/log/nginx/error.log
tail -f /var/log/apache2/error.log
```

### 3. Test Components
```bash
# Test configuration
php tests/test_config.php

# Test bot connection
php tests/TestRunner.php

# Test webhook
curl -X POST https://yourdomain.com/webhook.php \
  -H "Content-Type: application/json" \
  -d '{"message":{"text":"test"}}'
```

### 4. Verify Environment
```bash
# Check PHP version
php -v

# Check extensions
php -m | grep -E "(curl|json|mbstring)"

# Check permissions
ls -la storage/
ls -la config/
```

## Getting Help

### Before Asking for Help
1. Check this troubleshooting guide
2. Review error logs
3. Test with minimal configuration
4. Document exact error messages

### Where to Get Help
- **Documentation**: Check `docs/` directory
- **GitHub Issues**: Create detailed issue report
- **Telegram**: [@VEVoGamez](https://t.me/VEVoGamez)
- **Developer**: [@GoogleYooz](https://t.me/GoogleYooz)

### Information to Include
When reporting issues, include:
1. PHP version
2. Server environment (Apache/Nginx)
3. Exact error messages
4. Steps to reproduce
5. Configuration (without sensitive data)
6. Log excerpts

## Prevention Tips

### Regular Maintenance
1. Monitor logs regularly
2. Keep PHP and server updated
3. Backup configuration and data
4. Test after changes

### Best Practices
1. Use version control for configuration
2. Test in development environment first
3. Monitor resource usage
4. Implement proper error handling

### Security
1. Keep bot token secure
2. Regular security updates
3. Monitor for suspicious activity
4. Use HTTPS everywhere
