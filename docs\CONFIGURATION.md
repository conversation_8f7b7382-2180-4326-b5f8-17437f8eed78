# Configuration Guide

This guide covers all configuration options available in the VEVoGamez Telegram Bot.

## Table of Contents

- [Basic Configuration](#basic-configuration)
- [Advanced Configuration](#advanced-configuration)
- [Security Configuration](#security-configuration)
- [Feature Flags](#feature-flags)
- [File Storage Configuration](#file-storage-configuration)
- [Message Customization](#message-customization)
- [Environment-Specific Settings](#environment-specific-settings)

## Basic Configuration

### Bot Token and Admin Setup

Edit `config/config.php` to set up your bot:

```php
// Bot Configuration
define('BOT_TOKEN', '6859458694:AAGCOqJ-6lUwPW4dTL7bfVJ_pyBvA0mN_WI');
define('API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN . '/');

// Admin Configuration
define('MAIN_ADMIN_ID', 826813929);
define('MAX_ADMINS', 5);

// Channel Configuration
define('CHANNEL_USERNAME', '@VEVoGamez');
define('DEVELOPER_USERNAME', '@GoogleYooz');
```

#### Getting Your Bot Token

1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Send `/newbot` command
3. Follow the instructions to create your bot
4. Copy the token provided by BotFather

#### Finding Your User ID

1. Message [@userinfobot](https://t.me/userinfobot) on Telegram
2. The bot will reply with your user ID
3. Use this ID as `MAIN_ADMIN_ID`

### Basic Bot Information

```php
// Bot Settings
define('BOT_NAME', 'VEVoGamez Bot');
define('BOT_VERSION', '2.0.0');
define('BOT_DESCRIPTION', 'البوت 🤖 مخصص للتواصل مع فريق VEVoGamez...');
```

## Advanced Configuration

### File Storage Paths

```php
// File Storage Paths
define('STORAGE_PATH', __DIR__ . '/../storage/');
define('USERS_PATH', STORAGE_PATH . 'users/');
define('MESSAGES_PATH', STORAGE_PATH . 'messages/');
define('ADMIN_PATH', STORAGE_PATH . 'admin/');
define('LOGS_PATH', STORAGE_PATH . 'logs/');
```

### File Configuration Array

The `$config` array defines all file and directory paths:

```php
$config = [
    'files' => [
        'members' => USERS_PATH . 'members.txt',
        'admins' => ADMIN_PATH . 'admins.txt',
        'banned' => USERS_PATH . 'banned.txt',
        'channels' => ADMIN_PATH . 'channels.txt',
        'settings' => ADMIN_PATH . 'settings.json',
        'statistics' => ADMIN_PATH . 'statistics.json',
        'welcome_message' => ADMIN_PATH . 'welcome.txt',
        'auto_reply' => ADMIN_PATH . 'auto_reply.txt'
    ],
    'directories' => [
        'storage' => STORAGE_PATH,
        'users' => USERS_PATH,
        'messages' => MESSAGES_PATH,
        'admin' => ADMIN_PATH,
        'logs' => LOGS_PATH,
        'temp' => STORAGE_PATH . 'temp/',
        'backups' => STORAGE_PATH . 'backups/'
    ]
];
```

## Security Configuration

### Rate Limiting

```php
// Security Settings
define('MAX_MESSAGE_LENGTH', 4096);
define('RATE_LIMIT_MESSAGES', 20); // Messages per minute
define('RATE_LIMIT_TIME', 60); // Time window in seconds
```

### Security Middleware Configuration

Create `storage/admin/security_config.json`:

```json
{
    "blocked_words": [
        "spam",
        "scam",
        "inappropriate_word"
    ],
    "blocked_domains": [
        "malicious-site.com",
        "spam-domain.net"
    ],
    "max_file_size": 52428800,
    "allowed_file_types": [
        "photo",
        "video",
        "document",
        "audio",
        "voice",
        "sticker"
    ],
    "suspicious_patterns": [
        "/(.)\1{20,}/",
        "/[A-Z]{30,}/",
        "/(.{1,5})\1{10,}/"
    ]
}
```

## Feature Flags

Enable or disable specific features:

```php
// Feature Flags
define('ENABLE_SUBSCRIPTION_CHECK', true);
define('ENABLE_ADMIN_NOTIFICATIONS', true);
define('ENABLE_MESSAGE_LOGGING', true);
define('ENABLE_STATISTICS', true);
```

### Feature Flag Details

- **`ENABLE_SUBSCRIPTION_CHECK`**: Requires users to subscribe to channels
- **`ENABLE_ADMIN_NOTIFICATIONS`**: Sends notifications when new users join
- **`ENABLE_MESSAGE_LOGGING`**: Logs all messages for debugging
- **`ENABLE_STATISTICS`**: Tracks usage statistics

## File Storage Configuration

### Default File Initialization

The bot automatically creates default files with initial content:

```php
$defaultFiles = [
    $config['files']['members'] => '',
    $config['files']['admins'] => MAIN_ADMIN_ID . "\n",
    $config['files']['banned'] => '',
    $config['files']['channels'] => '',
    $config['files']['welcome_message'] => WELCOME_MESSAGE,
    $config['files']['auto_reply'] => AUTO_REPLY_MESSAGE,
    $config['files']['settings'] => json_encode([
        'notifications_enabled' => true,
        'subscription_check' => true,
        'auto_reply_enabled' => true,
        'media_restrictions' => [
            'photos' => false,
            'videos' => false,
            'documents' => false,
            'stickers' => false,
            'voice' => false,
            'audio' => false,
            'forwards' => false
        ]
    ], JSON_PRETTY_PRINT),
    $config['files']['statistics'] => json_encode([
        'total_users' => 0,
        'total_messages_received' => 0,
        'total_messages_sent' => 0,
        'total_broadcasts' => 0,
        'bot_started' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT)
];
```

### Settings File Structure

The `settings.json` file controls bot behavior:

```json
{
    "notifications_enabled": true,
    "subscription_check": true,
    "auto_reply_enabled": true,
    "bot_enabled": true,
    "media_restrictions": {
        "photos": false,
        "videos": false,
        "documents": false,
        "stickers": false,
        "voice": false,
        "audio": false,
        "forwards": false
    },
    "rate_limiting": {
        "enabled": true,
        "max_messages": 20,
        "time_window": 60
    }
}
```

## Message Customization

### Default Messages

```php
// Default Messages
define('WELCOME_MESSAGE', "اهلا بك عزيزي 🤍.\nالبوت 🤖 مخصص للتواصل مع فريق VEVoGamez...");
define('AUTO_REPLY_MESSAGE', 'تم استلام رسالتك وسيتم الرد عليك قريباً');
define('BANNED_MESSAGE', '❌ المعذرة لا تستطيع ارسال الرسائل انت محظور');
define('SUBSCRIPTION_REQUIRED_MESSAGE', "عذراً يجب عليك الاشتراك في القناه\nلتستطيع استخدام البوت ⚠️ 👇🏻");
```

### Error Messages

```php
// Error Messages
define('ERROR_GENERAL', 'حدث خطأ، يرجى المحاولة مرة أخرى');
define('ERROR_PERMISSION_DENIED', 'ليس لديك صلاحية لتنفيذ هذا الأمر');
define('ERROR_USER_NOT_FOUND', 'المستخدم غير موجود');
define('ERROR_INVALID_INPUT', 'البيانات المدخلة غير صحيحة');
```

### Success Messages

```php
// Success Messages
define('SUCCESS_ADMIN_ADDED', 'تم رفع المستخدم كأدمن بنجاح ✅');
define('SUCCESS_ADMIN_REMOVED', 'تم تنزيل المستخدم من الأدمنية ✅');
define('SUCCESS_USER_BANNED', 'تم حظر المستخدم بنجاح ✅');
define('SUCCESS_USER_UNBANNED', 'تم إلغاء حظر المستخدم بنجاح ✅');
define('SUCCESS_MESSAGE_SENT', 'تم إرسال الرسالة بنجاح ✅');
define('SUCCESS_BROADCAST_SENT', 'تم الإذاعة بنجاح 🎉');
```

### Custom Message Files

You can customize messages by editing these files:

- `storage/admin/welcome.txt` - Welcome message
- `storage/admin/auto_reply.txt` - Auto-reply message
- `storage/admin/subscription_message.txt` - Subscription required message

## Environment-Specific Settings

### Development Environment

For development, add these settings to `config/config.php`:

```php
// Development settings
if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
    define('DEBUG_MODE', true);
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    // Use different storage path for development
    define('STORAGE_PATH', __DIR__ . '/../storage_dev/');
}
```

### Production Environment

For production, ensure these settings:

```php
// Production settings
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', LOGS_PATH . 'php_errors.log');
```

### Server Configuration

#### Apache (.htaccess)

Create `.htaccess` in the root directory:

```apache
# Deny access to sensitive directories
<Directory "storage">
    Require all denied
</Directory>

<Directory "config">
    Require all denied
</Directory>

<Directory "src">
    Require all denied
</Directory>

# Allow only webhook.php and setup.php in public
<Directory "public">
    <Files "*">
        Require all denied
    </Files>
    <Files "webhook.php">
        Require all granted
    </Files>
    <Files "setup.php">
        Require all granted
    </Files>
</Directory>
```

#### Nginx

Add to your Nginx configuration:

```nginx
# Deny access to sensitive directories
location ~ ^/(storage|config|src)/ {
    deny all;
    return 404;
}

# Allow only specific files in public
location ~ ^/public/(webhook|setup)\.php$ {
    try_files $uri =404;
    fastcgi_pass php-fpm;
    fastcgi_index index.php;
    include fastcgi_params;
}
```

## Database Alternative Configuration

If you want to use a database instead of files, you can extend the FileService:

```php
// In config/config.php
$config['database'] = [
    'type' => 'mysql', // mysql, sqlite, postgresql
    'host' => 'localhost',
    'port' => 3306,
    'database' => 'telegram_bot',
    'username' => 'bot_user',
    'password' => 'secure_password',
    'charset' => 'utf8mb4'
];
```

## Backup Configuration

### Automated Backups

Add to your cron jobs:

```bash
# Daily backup at 2 AM
0 2 * * * /usr/bin/php /path/to/bot/scripts/backup.php

# Weekly cleanup at 3 AM on Sundays
0 3 * * 0 /usr/bin/php /path/to/bot/scripts/cleanup.php
```

### Backup Script Configuration

Create `scripts/backup_config.php`:

```php
<?php
return [
    'backup_path' => '/path/to/backups/',
    'retention_days' => 30,
    'compress' => true,
    'include_logs' => false,
    'exclude_patterns' => [
        '*.tmp',
        'temp/*',
        'cache/*'
    ]
];
```

## Monitoring Configuration

### Log Rotation

Configure log rotation in `/etc/logrotate.d/telegram-bot`:

```
/path/to/bot/storage/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 www-data www-data
}
```

### Health Check Configuration

Create `public/health.php`:

```php
<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/Bot.php';

$bot = new TelegramBot\Bot();
$stats = $bot->getStatistics();

header('Content-Type: application/json');
echo json_encode([
    'status' => 'healthy',
    'timestamp' => time(),
    'version' => BOT_VERSION,
    'uptime' => time() - strtotime($stats['bot_started']),
    'memory_usage' => memory_get_usage(true),
    'storage_writable' => is_writable(STORAGE_PATH)
]);
```

## Troubleshooting Configuration Issues

### Common Issues

1. **Permission Errors**
   ```bash
   chmod -R 755 storage/
   chown -R www-data:www-data storage/
   ```

2. **Token Issues**
   - Verify token format: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`
   - Check token validity with BotFather

3. **Webhook Issues**
   - Ensure HTTPS is enabled
   - Check webhook URL accessibility
   - Verify SSL certificate validity

### Debug Mode

Enable debug mode for troubleshooting:

```php
// In config/config.php
define('DEBUG_MODE', true);

// This will log all incoming updates
if (DEBUG_MODE) {
    $fileService->logMessage('debug', 'Incoming update', $update);
}
```

### Configuration Validation

Add configuration validation:

```php
// Validate configuration
function validateConfig() {
    $errors = [];
    
    if (!defined('BOT_TOKEN') || empty(BOT_TOKEN)) {
        $errors[] = 'BOT_TOKEN is not defined';
    }
    
    if (!defined('MAIN_ADMIN_ID') || !is_numeric(MAIN_ADMIN_ID)) {
        $errors[] = 'MAIN_ADMIN_ID is not valid';
    }
    
    if (!is_writable(STORAGE_PATH)) {
        $errors[] = 'Storage path is not writable';
    }
    
    return $errors;
}

$configErrors = validateConfig();
if (!empty($configErrors)) {
    die('Configuration errors: ' . implode(', ', $configErrors));
}
```
