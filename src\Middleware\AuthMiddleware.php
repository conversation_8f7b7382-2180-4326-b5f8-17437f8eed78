<?php

namespace TelegramBot\Middleware;

use TelegramBot\Services\UserService;
use TelegramBot\Services\FileService;

/**
 * Authentication Middleware
 * 
 * Handles user authentication and authorization checks.
 */
class AuthMiddleware
{
    private $userService;
    private $fileService;

    public function __construct(UserService $userService, FileService $fileService)
    {
        $this->userService = $userService;
        $this->fileService = $fileService;
    }

    /**
     * Check if user is authenticated (not banned)
     */
    public function isAuthenticated($userId)
    {
        return !$this->userService->isBanned($userId);
    }

    /**
     * Check if user has admin privileges
     */
    public function isAdmin($userId)
    {
        return $this->userService->isAdmin($userId);
    }

    /**
     * Check if user is main admin
     */
    public function isMainAdmin($userId)
    {
        return $this->userService->isMainAdmin($userId);
    }

    /**
     * Check if user has specific permission
     */
    public function hasPermission($userId, $permission)
    {
        return $this->userService->hasPermission($userId, $permission);
    }

    /**
     * Middleware to check authentication
     */
    public function authenticate($update, $next)
    {
        $userId = $this->extractUserId($update);
        
        if (!$userId) {
            return $this->denyAccess('Invalid user ID');
        }

        if (!$this->isAuthenticated($userId)) {
            return $this->denyAccess('User is banned');
        }

        // User is authenticated, continue to next middleware/handler
        return $next($update);
    }

    /**
     * Middleware to check admin privileges
     */
    public function requireAdmin($update, $next)
    {
        $userId = $this->extractUserId($update);
        
        if (!$this->isAdmin($userId)) {
            return $this->denyAccess('Admin privileges required');
        }

        return $next($update);
    }

    /**
     * Middleware to check main admin privileges
     */
    public function requireMainAdmin($update, $next)
    {
        $userId = $this->extractUserId($update);
        
        if (!$this->isMainAdmin($userId)) {
            return $this->denyAccess('Main admin privileges required');
        }

        return $next($update);
    }

    /**
     * Middleware to check specific permission
     */
    public function requirePermission($permission)
    {
        return function($update, $next) use ($permission) {
            $userId = $this->extractUserId($update);
            
            if (!$this->hasPermission($userId, $permission)) {
                return $this->denyAccess("Permission '$permission' required");
            }

            return $next($update);
        };
    }

    /**
     * Extract user ID from update
     */
    private function extractUserId($update)
    {
        if (isset($update['message']['from']['id'])) {
            return $update['message']['from']['id'];
        }
        
        if (isset($update['callback_query']['from']['id'])) {
            return $update['callback_query']['from']['id'];
        }
        
        if (isset($update['inline_query']['from']['id'])) {
            return $update['inline_query']['from']['id'];
        }

        return null;
    }

    /**
     * Deny access with reason
     */
    private function denyAccess($reason)
    {
        $this->fileService->logMessage('warning', 'Access denied: ' . $reason);
        return false;
    }

    /**
     * Check if user can perform action on target user
     */
    public function canManageUser($adminId, $targetId)
    {
        // Main admin can manage anyone except themselves for certain actions
        if ($this->isMainAdmin($adminId)) {
            return true;
        }

        // Regular admins cannot manage other admins
        if ($this->isAdmin($targetId)) {
            return false;
        }

        // Admin can manage regular users
        return $this->isAdmin($adminId);
    }

    /**
     * Check rate limiting for user actions
     */
    public function checkRateLimit($userId, $action = 'general')
    {
        return $this->userService->checkRateLimit($userId);
    }

    /**
     * Log authentication attempt
     */
    public function logAuthAttempt($userId, $action, $success = true)
    {
        $this->fileService->logMessage(
            $success ? 'info' : 'warning',
            "Auth attempt: $action",
            [
                'user_id' => $userId,
                'action' => $action,
                'success' => $success,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]
        );
    }

    /**
     * Check if user session is valid
     */
    public function isValidSession($userId)
    {
        $sessionFile = STORAGE_PATH . "sessions/$userId.json";
        
        if (!file_exists($sessionFile)) {
            return true; // No session required for basic operations
        }

        $sessionData = json_decode(file_get_contents($sessionFile), true);
        
        if (!$sessionData) {
            return false;
        }

        // Check session expiry
        if (isset($sessionData['expires_at']) && $sessionData['expires_at'] < time()) {
            unlink($sessionFile);
            return false;
        }

        return true;
    }

    /**
     * Create user session
     */
    public function createSession($userId, $duration = 3600)
    {
        $sessionDir = STORAGE_PATH . 'sessions/';
        if (!is_dir($sessionDir)) {
            mkdir($sessionDir, 0755, true);
        }

        $sessionData = [
            'user_id' => $userId,
            'created_at' => time(),
            'expires_at' => time() + $duration,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        return file_put_contents(
            $sessionDir . "$userId.json",
            json_encode($sessionData, JSON_PRETTY_PRINT)
        );
    }

    /**
     * Destroy user session
     */
    public function destroySession($userId)
    {
        $sessionFile = STORAGE_PATH . "sessions/$userId.json";
        
        if (file_exists($sessionFile)) {
            return unlink($sessionFile);
        }

        return true;
    }

    /**
     * Clean expired sessions
     */
    public function cleanExpiredSessions()
    {
        $sessionDir = STORAGE_PATH . 'sessions/';
        
        if (!is_dir($sessionDir)) {
            return;
        }

        $sessionFiles = glob($sessionDir . '*.json');
        $cleaned = 0;

        foreach ($sessionFiles as $file) {
            $sessionData = json_decode(file_get_contents($file), true);
            
            if ($sessionData && isset($sessionData['expires_at']) && 
                $sessionData['expires_at'] < time()) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }

        $this->fileService->logMessage('info', "Cleaned $cleaned expired sessions");
    }

    /**
     * Get user permissions
     */
    public function getUserPermissions($userId)
    {
        $permissions = ['send_message', 'receive_message'];

        if ($this->isAdmin($userId)) {
            $permissions = array_merge($permissions, [
                'admin_panel',
                'view_statistics',
                'ban_users',
                'broadcast'
            ]);
        }

        if ($this->isMainAdmin($userId)) {
            $permissions = array_merge($permissions, [
                'manage_admins',
                'manage_channels',
                'manage_settings',
                'view_logs'
            ]);
        }

        return $permissions;
    }

    /**
     * Check if action requires elevated privileges
     */
    public function requiresElevatedPrivileges($action)
    {
        $elevatedActions = [
            'manage_admins',
            'manage_channels',
            'delete_webhook',
            'view_logs',
            'system_maintenance'
        ];

        return in_array($action, $elevatedActions);
    }
}
