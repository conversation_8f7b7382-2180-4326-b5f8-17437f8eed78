<?php
/**
 * Configuration Test Script
 * 
 * This script tests if the configuration loads correctly without warnings.
 */

echo "🧪 Testing Configuration Loading\n";
echo "================================\n\n";

// Test 1: Load configuration once
echo "Test 1: Loading configuration for the first time... ";
try {
    $config1 = require __DIR__ . '/../config/config.php';
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 2: Load configuration again (should not cause warnings)
echo "Test 2: Loading configuration again (testing include guards)... ";
try {
    $config2 = require __DIR__ . '/../config/config.php';
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

// Test 3: Check if constants are defined
echo "Test 3: Checking if constants are properly defined... ";
$requiredConstants = [
    'BOT_TOKEN',
    'MAIN_ADMIN_ID',
    'STORAGE_PATH',
    'WELCOME_MESSAGE',
    'BOT_VERSION'
];

$missingConstants = [];
foreach ($requiredConstants as $constant) {
    if (!defined($constant)) {
        $missingConstants[] = $constant;
    }
}

if (empty($missingConstants)) {
    echo "✅ SUCCESS\n";
} else {
    echo "❌ FAILED: Missing constants: " . implode(', ', $missingConstants) . "\n";
}

// Test 4: Check if directories exist
echo "Test 4: Checking if storage directories exist... ";
$requiredDirs = [
    STORAGE_PATH,
    USERS_PATH,
    ADMIN_PATH,
    LOGS_PATH,
    MESSAGES_PATH
];

$missingDirs = [];
foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        $missingDirs[] = $dir;
    }
}

if (empty($missingDirs)) {
    echo "✅ SUCCESS\n";
} else {
    echo "❌ FAILED: Missing directories: " . implode(', ', $missingDirs) . "\n";
}

// Test 5: Test multiple includes (simulate real usage)
echo "Test 5: Testing multiple includes (simulating real bot usage)... ";
try {
    // Simulate how the bot loads config multiple times
    require_once __DIR__ . '/../config/config.php';
    include __DIR__ . '/../config/config.php';
    require __DIR__ . '/../config/config.php';
    echo "✅ SUCCESS\n";
} catch (Exception $e) {
    echo "❌ FAILED: " . $e->getMessage() . "\n";
}

echo "\n================================\n";
echo "Configuration test completed!\n";
echo "If all tests passed, the configuration is working correctly.\n";
echo "================================\n";
