<?php
/**
 * VEVoGamez Telegram Bot Installation Script
 * 
 * This script helps you set up the bot with proper configuration and permissions.
 */

echo "🤖 VEVoGamez Telegram Bot Installation\n";
echo "=====================================\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    die("❌ PHP 7.4 or higher is required. Current version: " . PHP_VERSION . "\n");
}

// Check required extensions
$requiredExtensions = ['curl', 'json', 'mbstring'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    die("❌ Missing required PHP extensions: " . implode(', ', $missingExtensions) . "\n");
}

echo "✅ PHP version and extensions check passed\n\n";

// Define paths
$rootDir = dirname(__DIR__);
$configDir = $rootDir . '/config';
$storageDir = $rootDir . '/storage';
$publicDir = $rootDir . '/public';

// Create directories
$directories = [
    $storageDir,
    $storageDir . '/admin',
    $storageDir . '/users',
    $storageDir . '/messages',
    $storageDir . '/logs',
    $storageDir . '/temp',
    $storageDir . '/backups',
    $storageDir . '/security',
    $storageDir . '/sessions'
];

echo "📁 Creating directories...\n";
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "   ✅ Created: $dir\n";
        } else {
            echo "   ❌ Failed to create: $dir\n";
        }
    } else {
        echo "   ✅ Exists: $dir\n";
    }
}

// Set permissions
echo "\n🔒 Setting permissions...\n";
$permissions = [
    $storageDir => 0755,
    $configDir => 0755,
    $publicDir => 0755
];

foreach ($permissions as $path => $perm) {
    if (chmod($path, $perm)) {
        echo "   ✅ Set permissions for: $path\n";
    } else {
        echo "   ❌ Failed to set permissions for: $path\n";
    }
}

// Check if config exists
$configFile = $configDir . '/config.php';
$exampleConfigFile = $configDir . '/config.example.php';

if (!file_exists($configFile)) {
    if (file_exists($exampleConfigFile)) {
        echo "\n📝 Configuration setup...\n";
        echo "Config file not found. Let's create one!\n\n";
        
        // Get bot token
        echo "Please enter your bot token (from @BotFather): ";
        $botToken = trim(fgets(STDIN));
        
        if (empty($botToken)) {
            die("❌ Bot token is required!\n");
        }
        
        // Validate token format
        if (!preg_match('/^\d+:[A-Za-z0-9_-]+$/', $botToken)) {
            die("❌ Invalid bot token format!\n");
        }
        
        // Get admin ID
        echo "Please enter your Telegram user ID (main admin): ";
        $adminId = trim(fgets(STDIN));
        
        if (!is_numeric($adminId)) {
            die("❌ Admin ID must be numeric!\n");
        }
        
        // Get channel username (optional)
        echo "Please enter your channel username (optional, press Enter to skip): ";
        $channelUsername = trim(fgets(STDIN));
        
        if (!empty($channelUsername) && !str_starts_with($channelUsername, '@')) {
            $channelUsername = '@' . $channelUsername;
        }
        
        // Create config file
        $configContent = file_get_contents($exampleConfigFile);
        $configContent = str_replace('YOUR_BOT_TOKEN_HERE', $botToken, $configContent);
        $configContent = str_replace('123456789', $adminId, $configContent);
        
        if (!empty($channelUsername)) {
            $configContent = str_replace('@YourChannel', $channelUsername, $configContent);
        }
        
        if (file_put_contents($configFile, $configContent)) {
            echo "   ✅ Configuration file created successfully!\n";
        } else {
            die("   ❌ Failed to create configuration file!\n");
        }
    } else {
        die("❌ Example configuration file not found!\n");
    }
} else {
    echo "\n✅ Configuration file already exists\n";
}

// Initialize bot
echo "\n🚀 Initializing bot...\n";

try {
    require_once $configFile;
    require_once $rootDir . '/src/TelegramAPI.php';
    require_once $rootDir . '/src/Services/FileService.php';
    require_once $rootDir . '/src/Bot.php';
    
    $bot = new TelegramBot\Bot();
    
    // Test bot connection
    echo "   🔍 Testing bot connection...\n";
    $botInfo = $bot->getBotInfo();
    
    if ($botInfo) {
        echo "   ✅ Bot connected successfully!\n";
        echo "   📋 Bot Name: " . $botInfo['first_name'] . "\n";
        echo "   📋 Bot Username: @" . $botInfo['username'] . "\n";
    } else {
        echo "   ❌ Failed to connect to bot. Please check your token.\n";
    }
    
    // Initialize files
    echo "   📄 Initializing data files...\n";
    $config = require $configFile;
    $fileService = new TelegramBot\Services\FileService($config);
    
    // Create default files
    $defaultFiles = [
        $config['files']['members'] => '',
        $config['files']['admins'] => MAIN_ADMIN_ID . "\n",
        $config['files']['banned'] => '',
        $config['files']['channels'] => !empty($channelUsername) ? $channelUsername . "\n" : '',
        $config['files']['welcome_message'] => WELCOME_MESSAGE,
        $config['files']['auto_reply'] => AUTO_REPLY_MESSAGE
    ];
    
    foreach ($defaultFiles as $file => $content) {
        if (!file_exists($file)) {
            if (file_put_contents($file, $content) !== false) {
                echo "   ✅ Created: " . basename($file) . "\n";
            } else {
                echo "   ❌ Failed to create: " . basename($file) . "\n";
            }
        }
    }
    
    // Create JSON files
    $jsonFiles = [
        $config['files']['settings'] => [
            'notifications_enabled' => true,
            'subscription_check' => !empty($channelUsername),
            'auto_reply_enabled' => true,
            'bot_enabled' => true,
            'media_restrictions' => [
                'photos' => false,
                'videos' => false,
                'documents' => false,
                'stickers' => false,
                'voice' => false,
                'audio' => false,
                'forwards' => false
            ]
        ],
        $config['files']['statistics'] => [
            'total_users' => 0,
            'total_messages_received' => 0,
            'total_messages_sent' => 0,
            'total_broadcasts' => 0,
            'bot_started' => date('Y-m-d H:i:s')
        ]
    ];
    
    foreach ($jsonFiles as $file => $data) {
        if (!file_exists($file)) {
            if (file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT)) !== false) {
                echo "   ✅ Created: " . basename($file) . "\n";
            } else {
                echo "   ❌ Failed to create: " . basename($file) . "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Error during initialization: " . $e->getMessage() . "\n";
}

// Security recommendations
echo "\n🔒 Security Recommendations:\n";
echo "   1. Set proper file permissions (already done)\n";
echo "   2. Use HTTPS for webhook (required by Telegram)\n";
echo "   3. Keep your bot token secret\n";
echo "   4. Regularly update the bot code\n";
echo "   5. Monitor logs for suspicious activity\n";

// Next steps
echo "\n📋 Next Steps:\n";
echo "   1. Upload the bot files to your web server\n";
echo "   2. Ensure your domain has a valid SSL certificate\n";
echo "   3. Access the setup page: https://yourdomain.com/path/to/bot/public/setup.php\n";
echo "   4. Set the webhook URL\n";
echo "   5. Test the bot by sending /start\n";

// Generate webhook URL suggestion
if (isset($_SERVER['HTTP_HOST'])) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['REQUEST_URI']);
    $webhookUrl = $protocol . '://' . $host . $path . '/public/webhook.php';
    
    echo "\n🔗 Suggested webhook URL:\n";
    echo "   $webhookUrl\n";
}

// Test runner suggestion
echo "\n🧪 Testing:\n";
echo "   Run tests with: php tests/TestRunner.php\n";

// Documentation links
echo "\n📚 Documentation:\n";
echo "   - README.md - General information and setup\n";
echo "   - docs/API.md - API documentation\n";
echo "   - docs/CONFIGURATION.md - Configuration guide\n";
echo "   - docs/DEPLOYMENT.md - Deployment guide\n";
echo "   - docs/SECURITY.md - Security guide\n";
echo "   - docs/MIGRATION.md - Migration from old version\n";

echo "\n✅ Installation completed successfully!\n";
echo "🎉 Your VEVoGamez Telegram Bot is ready to use!\n\n";

// Create .htaccess for Apache
$htaccessFile = $rootDir . '/.htaccess';
if (!file_exists($htaccessFile)) {
    $htaccessContent = '# VEVoGamez Telegram Bot Security
# Deny access to sensitive directories
<Directory "storage">
    Require all denied
</Directory>

<Directory "config">
    Require all denied
</Directory>

<Directory "src">
    Require all denied
</Directory>

<Directory "tests">
    Require all denied
</Directory>

<Directory "scripts">
    Require all denied
</Directory>

# Allow only specific files in public
<Directory "public">
    <Files "*">
        Require all denied
    </Files>
    <Files "webhook.php">
        Require all granted
    </Files>
    <Files "setup.php">
        Require all granted
    </Files>
</Directory>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Hide sensitive files
<Files "*.log">
    Require all denied
</Files>

<Files "*.txt">
    Require all denied
</Files>

<Files "*.json">
    Require all denied
</Files>
';
    
    if (file_put_contents($htaccessFile, $htaccessContent)) {
        echo "✅ Created .htaccess file for Apache security\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Thank you for using VEVoGamez Telegram Bot!\n";
echo "For support: @GoogleYooz | @VEVoGamez\n";
echo str_repeat("=", 50) . "\n";
