<?php

namespace TelegramBot;

/**
 * Main Bot Class
 * 
 * The main entry point for the Telegram bot that handles webhook requests
 * and coordinates all bot functionality.
 */
class Bot
{
    private $api;
    private $router;
    private $config;

    public function __construct()
    {
        // Load configuration
        $this->config = require __DIR__ . '/../config/config.php';
        
        // Initialize API
        $this->api = new TelegramAPI();
        
        // Initialize router
        $this->router = new Router($this->api, $this->config);
    }

    /**
     * Handle incoming webhook request
     */
    public function handleWebhook()
    {
        try {
            // Get update from Telegram
            $input = file_get_contents('php://input');
            
            if (empty($input)) {
                $this->sendError('No input received');
                return false;
            }

            $update = json_decode($input, true);
            
            if (!$update) {
                $this->sendError('Invalid JSON received');
                return false;
            }

            // Log incoming update if debugging is enabled
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $this->logDebug('Incoming update', $update);
            }

            // Route the update
            $result = $this->router->route($update);
            
            if (!$result) {
                $this->logError('Failed to process update', $update);
            }

            return $result;

        } catch (Exception $e) {
            $this->logError('Bot error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * Set webhook URL
     */
    public function setWebhook($url, $options = [])
    {
        try {
            $result = $this->api->setWebhook($url, $options);
            
            if ($result && $result['ok']) {
                $this->logInfo('Webhook set successfully', ['url' => $url]);
                return true;
            } else {
                $error = $result['description'] ?? 'Unknown error';
                $this->logError('Failed to set webhook: ' . $error, ['url' => $url]);
                return false;
            }
        } catch (Exception $e) {
            $this->logError('Exception setting webhook: ' . $e->getMessage(), ['url' => $url]);
            return false;
        }
    }

    /**
     * Delete webhook
     */
    public function deleteWebhook()
    {
        try {
            $result = $this->api->deleteWebhook();
            
            if ($result && $result['ok']) {
                $this->logInfo('Webhook deleted successfully');
                return true;
            } else {
                $error = $result['description'] ?? 'Unknown error';
                $this->logError('Failed to delete webhook: ' . $error);
                return false;
            }
        } catch (Exception $e) {
            $this->logError('Exception deleting webhook: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get webhook info
     */
    public function getWebhookInfo()
    {
        try {
            $result = $this->api->getWebhookInfo();
            
            if ($result && $result['ok']) {
                return $result['result'];
            } else {
                $error = $result['description'] ?? 'Unknown error';
                $this->logError('Failed to get webhook info: ' . $error);
                return false;
            }
        } catch (Exception $e) {
            $this->logError('Exception getting webhook info: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send test message to verify bot is working
     */
    public function sendTestMessage($chatId = null)
    {
        $chatId = $chatId ?: MAIN_ADMIN_ID;
        
        $message = "🤖 Bot Test Message\n\n";
        $message .= "✅ Bot is working correctly!\n";
        $message .= "📅 Time: " . date('Y-m-d H:i:s') . "\n";
        $message .= "🔧 Version: " . BOT_VERSION;

        try {
            $result = $this->api->sendMessage($chatId, $message);
            
            if ($result && $result['ok']) {
                $this->logInfo('Test message sent successfully', ['chat_id' => $chatId]);
                return true;
            } else {
                $error = $result['description'] ?? 'Unknown error';
                $this->logError('Failed to send test message: ' . $error, ['chat_id' => $chatId]);
                return false;
            }
        } catch (Exception $e) {
            $this->logError('Exception sending test message: ' . $e->getMessage(), ['chat_id' => $chatId]);
            return false;
        }
    }

    /**
     * Get bot information
     */
    public function getBotInfo()
    {
        try {
            $result = $this->api->makeRequest('getMe');
            
            if ($result && $result['ok']) {
                return $result['result'];
            } else {
                $error = $result['description'] ?? 'Unknown error';
                $this->logError('Failed to get bot info: ' . $error);
                return false;
            }
        } catch (Exception $e) {
            $this->logError('Exception getting bot info: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Perform maintenance tasks
     */
    public function performMaintenance()
    {
        try {
            $this->logInfo('Starting maintenance tasks');

            // Clean old logs (keep last 30 days)
            $this->cleanOldLogs(30);

            // Clean old message mappings (keep last 7 days)
            $this->cleanOldMessageMappings(7);

            // Clean old broadcast files (keep last 7 days)
            $this->cleanOldBroadcasts(7);

            // Clean old rate limit files (keep last 1 day)
            $this->cleanOldRateLimitFiles(1);

            $this->logInfo('Maintenance tasks completed');
            return true;

        } catch (Exception $e) {
            $this->logError('Maintenance error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean old log files
     */
    private function cleanOldLogs($days)
    {
        $cutoffDate = date('Y-m-d', strtotime("-$days days"));
        $logFiles = glob(LOGS_PATH . '*.log');
        $cleaned = 0;
        
        foreach ($logFiles as $file) {
            $fileDate = basename($file, '.log');
            if ($fileDate < $cutoffDate) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }

        $this->logInfo("Cleaned $cleaned old log files");
    }

    /**
     * Clean old message mappings
     */
    private function cleanOldMessageMappings($days)
    {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $mappingFiles = glob(MESSAGES_PATH . '*.txt');
        $cleaned = 0;
        
        foreach ($mappingFiles as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }

        $this->logInfo("Cleaned $cleaned old message mappings");
    }

    /**
     * Clean old broadcast files
     */
    private function cleanOldBroadcasts($days)
    {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $broadcastFiles = glob(STORAGE_PATH . 'broadcasts/*.txt');
        $cleaned = 0;
        
        foreach ($broadcastFiles as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }

        $this->logInfo("Cleaned $cleaned old broadcast files");
    }

    /**
     * Clean old rate limit files
     */
    private function cleanOldRateLimitFiles($days)
    {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $rateLimitFiles = glob(STORAGE_PATH . 'rate_limit/*.json');
        $cleaned = 0;
        
        foreach ($rateLimitFiles as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }

        $this->logInfo("Cleaned $cleaned old rate limit files");
    }

    /**
     * Get bot statistics
     */
    public function getStatistics()
    {
        try {
            $fileService = new \TelegramBot\Services\FileService($this->config);
            $stats = $fileService->getStatistics();
            
            // Add additional runtime stats
            $stats['memory_usage'] = memory_get_usage(true);
            $stats['memory_peak'] = memory_get_peak_usage(true);
            $stats['php_version'] = PHP_VERSION;
            $stats['bot_version'] = BOT_VERSION;
            
            return $stats;
        } catch (Exception $e) {
            $this->logError('Error getting statistics: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Log info message
     */
    private function logInfo($message, $context = [])
    {
        $this->log('info', $message, $context);
    }

    /**
     * Log error message
     */
    private function logError($message, $context = [])
    {
        $this->log('error', $message, $context);
    }

    /**
     * Log debug message
     */
    private function logDebug($message, $context = [])
    {
        $this->log('debug', $message, $context);
    }

    /**
     * Generic log method
     */
    private function log($level, $message, $context = [])
    {
        try {
            $fileService = new \TelegramBot\Services\FileService($this->config);
            $fileService->logMessage($level, $message, $context);
        } catch (Exception $e) {
            // Fallback logging to error_log if file service fails
            error_log("Bot Log Error: " . $e->getMessage());
            error_log("Original message: [$level] $message");
        }
    }

    /**
     * Send error response
     */
    private function sendError($message)
    {
        http_response_code(400);
        echo json_encode(['error' => $message]);
    }

    /**
     * Get API instance
     */
    public function getApi()
    {
        return $this->api;
    }

    /**
     * Get router instance
     */
    public function getRouter()
    {
        return $this->router;
    }

    /**
     * Get configuration
     */
    public function getConfig()
    {
        return $this->config;
    }
}
