<?php
/**
 * Fix Constants Script
 * 
 * This script helps fix constant redefinition issues by clearing the PHP opcache
 * and providing diagnostic information.
 */

echo "🔧 Constants Fix Script\n";
echo "=======================\n\n";

// Clear opcache if available
if (function_exists('opcache_reset')) {
    echo "Clearing OPcache... ";
    if (opcache_reset()) {
        echo "✅ SUCCESS\n";
    } else {
        echo "❌ FAILED\n";
    }
} else {
    echo "OPcache not available (this is normal)\n";
}

// Check for multiple config inclusions
echo "\nChecking for configuration issues...\n";

// Get all defined constants before loading config
$constantsBefore = get_defined_constants(true)['user'] ?? [];

// Load config
try {
    $config = require __DIR__ . '/../config/config.php';
    echo "✅ Configuration loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Configuration loading failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Get all defined constants after loading config
$constantsAfter = get_defined_constants(true)['user'] ?? [];

// Find newly defined constants
$newConstants = array_diff_key($constantsAfter, $constantsBefore);

echo "\nBot constants defined:\n";
$botConstants = [];
foreach ($newConstants as $name => $value) {
    if (strpos($name, 'BOT_') === 0 || 
        strpos($name, 'MAIN_') === 0 || 
        strpos($name, 'CHANNEL_') === 0 ||
        strpos($name, 'STORAGE_') === 0 ||
        strpos($name, 'USERS_') === 0 ||
        strpos($name, 'ADMIN_') === 0 ||
        strpos($name, 'LOGS_') === 0 ||
        strpos($name, 'MESSAGES_') === 0 ||
        strpos($name, 'WELCOME_') === 0 ||
        strpos($name, 'ERROR_') === 0 ||
        strpos($name, 'SUCCESS_') === 0 ||
        strpos($name, 'ENABLE_') === 0 ||
        strpos($name, 'RATE_') === 0 ||
        strpos($name, 'MAX_') === 0) {
        $botConstants[$name] = $value;
        echo "  ✅ $name\n";
    }
}

echo "\nTotal bot constants: " . count($botConstants) . "\n";

// Test multiple inclusions
echo "\nTesting multiple inclusions...\n";

// Capture any warnings/errors
$errors = [];
set_error_handler(function($severity, $message, $file, $line) use (&$errors) {
    $errors[] = "[$severity] $message in $file on line $line";
});

// Try to include config multiple times
for ($i = 1; $i <= 3; $i++) {
    echo "  Inclusion $i... ";
    try {
        include __DIR__ . '/../config/config.php';
        echo "✅ OK\n";
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
    }
}

// Restore error handler
restore_error_handler();

if (!empty($errors)) {
    echo "\n⚠️  Warnings/Errors detected:\n";
    foreach ($errors as $error) {
        echo "  $error\n";
    }
    
    echo "\n🔧 Suggested fixes:\n";
    echo "  1. Make sure you're using the updated config.php with include guards\n";
    echo "  2. Clear any PHP opcache: opcache_reset()\n";
    echo "  3. Restart your web server\n";
    echo "  4. Check for any old cached files\n";
} else {
    echo "\n✅ No constant redefinition issues detected!\n";
}

// Check file permissions
echo "\nChecking file permissions...\n";
$files = [
    __DIR__ . '/../config/config.php',
    __DIR__ . '/../storage/',
    __DIR__ . '/../storage/admin/',
    __DIR__ . '/../storage/users/',
    __DIR__ . '/../storage/logs/'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        $readable = is_readable($file) ? '✅' : '❌';
        $writable = is_writable($file) ? '✅' : '❌';
        echo "  $file: $perms (R:$readable W:$writable)\n";
    } else {
        echo "  ❌ $file: NOT FOUND\n";
    }
}

// Memory usage
echo "\nMemory usage: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB\n";
echo "Peak memory: " . round(memory_get_peak_usage(true) / 1024 / 1024, 2) . " MB\n";

echo "\n=======================\n";
echo "Fix script completed!\n";
echo "=======================\n";
