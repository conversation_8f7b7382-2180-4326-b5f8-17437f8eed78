<?php
/**
 * Simple Test Runner for Telegram Bot
 * 
 * This is a basic test runner to verify bot functionality.
 * For production, consider using PHPUnit or similar testing frameworks.
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/TelegramAPI.php';
require_once __DIR__ . '/../src/Services/FileService.php';
require_once __DIR__ . '/../src/Services/UserService.php';
require_once __DIR__ . '/../src/Services/MessageService.php';
require_once __DIR__ . '/../src/Services/ValidationService.php';
require_once __DIR__ . '/../src/Utils/Helper.php';
require_once __DIR__ . '/../src/Bot.php';

use TelegramBot\Bot;
use TelegramBot\TelegramAPI;
use TelegramBot\Services\FileService;
use TelegramBot\Services\UserService;
use TelegramBot\Services\ValidationService;
use TelegramBot\Utils\Helper;

class TestRunner
{
    private $tests = [];
    private $passed = 0;
    private $failed = 0;

    public function __construct()
    {
        echo "🧪 Starting Telegram Bot Tests\n";
        echo "================================\n\n";
    }

    /**
     * Add a test
     */
    public function addTest($name, $callback)
    {
        $this->tests[] = ['name' => $name, 'callback' => $callback];
    }

    /**
     * Run all tests
     */
    public function run()
    {
        foreach ($this->tests as $test) {
            $this->runTest($test['name'], $test['callback']);
        }

        $this->printSummary();
    }

    /**
     * Run individual test
     */
    private function runTest($name, $callback)
    {
        echo "Testing: {$name}... ";
        
        try {
            $result = $callback();
            
            if ($result === true) {
                echo "✅ PASSED\n";
                $this->passed++;
            } else {
                echo "❌ FAILED: {$result}\n";
                $this->failed++;
            }
        } catch (Exception $e) {
            echo "❌ ERROR: " . $e->getMessage() . "\n";
            $this->failed++;
        }
    }

    /**
     * Print test summary
     */
    private function printSummary()
    {
        echo "\n================================\n";
        echo "Test Summary:\n";
        echo "✅ Passed: {$this->passed}\n";
        echo "❌ Failed: {$this->failed}\n";
        echo "📊 Total: " . ($this->passed + $this->failed) . "\n";
        
        if ($this->failed === 0) {
            echo "\n🎉 All tests passed!\n";
        } else {
            echo "\n⚠️  Some tests failed. Please check the output above.\n";
        }
    }

    /**
     * Assert that condition is true
     */
    public static function assertTrue($condition, $message = 'Assertion failed')
    {
        if (!$condition) {
            throw new Exception($message);
        }
        return true;
    }

    /**
     * Assert that two values are equal
     */
    public static function assertEqual($expected, $actual, $message = 'Values are not equal')
    {
        if ($expected !== $actual) {
            throw new Exception("{$message}. Expected: {$expected}, Actual: {$actual}");
        }
        return true;
    }

    /**
     * Assert that value is not null
     */
    public static function assertNotNull($value, $message = 'Value is null')
    {
        if ($value === null) {
            throw new Exception($message);
        }
        return true;
    }

    /**
     * Assert that array contains key
     */
    public static function assertArrayHasKey($key, $array, $message = 'Array does not contain key')
    {
        if (!array_key_exists($key, $array)) {
            throw new Exception("{$message}: {$key}");
        }
        return true;
    }
}

// Initialize test runner
$testRunner = new TestRunner();

// Test 1: Configuration Loading
$testRunner->addTest('Configuration Loading', function() {
    TestRunner::assertTrue(defined('BOT_TOKEN'), 'BOT_TOKEN not defined');
    TestRunner::assertTrue(defined('MAIN_ADMIN_ID'), 'MAIN_ADMIN_ID not defined');
    TestRunner::assertTrue(is_dir(STORAGE_PATH), 'Storage directory does not exist');
    return true;
});

// Test 2: File Service
$testRunner->addTest('File Service', function() {
    $config = require __DIR__ . '/../config/config.php';
    $fileService = new FileService($config);
    
    // Test file operations
    $testFile = STORAGE_PATH . 'test.txt';
    $testContent = 'Test content';
    
    $fileService->writeFile($testFile, $testContent);
    $readContent = $fileService->readFile($testFile);
    
    TestRunner::assertEqual($testContent, $readContent, 'File content mismatch');
    
    // Cleanup
    if (file_exists($testFile)) {
        unlink($testFile);
    }
    
    return true;
});

// Test 3: User Service
$testRunner->addTest('User Service', function() {
    $config = require __DIR__ . '/../config/config.php';
    $fileService = new FileService($config);
    $api = new TelegramAPI();
    $userService = new UserService($fileService, $api);
    
    // Test admin check
    $isMainAdmin = $userService->isMainAdmin(MAIN_ADMIN_ID);
    TestRunner::assertTrue($isMainAdmin, 'Main admin check failed');
    
    // Test non-admin user
    $isAdmin = $userService->isAdmin(999999999);
    TestRunner::assertTrue(!$isAdmin, 'Non-admin user returned as admin');
    
    return true;
});

// Test 4: Validation Service
$testRunner->addTest('Validation Service', function() {
    $validationService = new ValidationService();
    
    // Test user ID validation
    TestRunner::assertTrue($validationService->validateUserId(123456789), 'Valid user ID failed validation');
    TestRunner::assertTrue(!$validationService->validateUserId('invalid'), 'Invalid user ID passed validation');
    
    // Test message text validation
    TestRunner::assertTrue($validationService->validateMessageText('Hello world'), 'Valid message failed validation');
    TestRunner::assertTrue(!$validationService->validateMessageText(''), 'Empty message passed validation');
    
    // Test spam detection
    TestRunner::assertTrue($validationService->isSpam('aaaaaaaaaaaaaaaaaaaaaa'), 'Spam not detected');
    TestRunner::assertTrue(!$validationService->isSpam('Normal message'), 'Normal message detected as spam');
    
    return true;
});

// Test 5: Helper Functions
$testRunner->addTest('Helper Functions', function() {
    // Test file size formatting
    $formatted = Helper::formatFileSize(1024);
    TestRunner::assertEqual('1 KB', $formatted, 'File size formatting failed');
    
    // Test text truncation
    $truncated = Helper::truncateText('This is a long text', 10);
    TestRunner::assertEqual('This is a...', $truncated, 'Text truncation failed');
    
    // Test username formatting
    $username = Helper::formatUsername('testuser');
    TestRunner::assertEqual('@testuser', $username, 'Username formatting failed');
    
    // Test random string generation
    $randomString = Helper::generateRandomString(10);
    TestRunner::assertEqual(10, strlen($randomString), 'Random string length incorrect');
    
    return true;
});

// Test 6: Telegram API (Basic)
$testRunner->addTest('Telegram API Basic', function() {
    $api = new TelegramAPI();
    
    // Test API URL construction
    TestRunner::assertTrue(strpos($api->getApiUrl(), BOT_TOKEN) !== false, 'API URL does not contain token');
    
    // Test keyboard creation
    $keyboard = TelegramAPI::createInlineKeyboard([
        [['text' => 'Test', 'callback_data' => 'test']]
    ]);
    
    TestRunner::assertTrue(is_string($keyboard), 'Keyboard is not a string');
    TestRunner::assertTrue(json_decode($keyboard) !== null, 'Keyboard is not valid JSON');
    
    return true;
});

// Test 7: Bot Initialization
$testRunner->addTest('Bot Initialization', function() {
    $bot = new Bot();
    
    TestRunner::assertNotNull($bot, 'Bot initialization failed');
    TestRunner::assertNotNull($bot->getApi(), 'Bot API not initialized');
    TestRunner::assertNotNull($bot->getRouter(), 'Bot router not initialized');
    
    return true;
});

// Test 8: Statistics
$testRunner->addTest('Statistics', function() {
    $bot = new Bot();
    $stats = $bot->getStatistics();
    
    TestRunner::assertNotNull($stats, 'Statistics not available');
    TestRunner::assertArrayHasKey('total_users', $stats, 'Statistics missing total_users');
    TestRunner::assertArrayHasKey('bot_started', $stats, 'Statistics missing bot_started');
    
    return true;
});

// Test 9: Directory Permissions
$testRunner->addTest('Directory Permissions', function() {
    $directories = [
        STORAGE_PATH,
        USERS_PATH,
        MESSAGES_PATH,
        ADMIN_PATH,
        LOGS_PATH
    ];
    
    foreach ($directories as $dir) {
        TestRunner::assertTrue(is_dir($dir), "Directory does not exist: {$dir}");
        TestRunner::assertTrue(is_writable($dir), "Directory is not writable: {$dir}");
    }
    
    return true;
});

// Test 10: JSON Configuration Files
$testRunner->addTest('JSON Configuration Files', function() {
    $config = require __DIR__ . '/../config/config.php';
    $fileService = new FileService($config);
    
    // Test settings file
    $settings = $fileService->getSettings();
    TestRunner::assertNotNull($settings, 'Settings file not readable');
    TestRunner::assertArrayHasKey('notifications_enabled', $settings, 'Settings missing notifications_enabled');
    
    // Test statistics file
    $statistics = $fileService->getStatistics();
    TestRunner::assertNotNull($statistics, 'Statistics file not readable');
    TestRunner::assertArrayHasKey('total_users', $statistics, 'Statistics missing total_users');
    
    return true;
});

// Test 11: Security Features
$testRunner->addTest('Security Features', function() {
    $validationService = new ValidationService();
    
    // Test SQL injection detection
    TestRunner::assertTrue($validationService->hasSqlInjection("'; DROP TABLE users; --"), 'SQL injection not detected');
    TestRunner::assertTrue(!$validationService->hasSqlInjection('Normal text'), 'Normal text flagged as SQL injection');
    
    // Test XSS detection
    TestRunner::assertTrue($validationService->hasXss('<script>alert("xss")</script>'), 'XSS not detected');
    TestRunner::assertTrue(!$validationService->hasXss('Normal text'), 'Normal text flagged as XSS');
    
    return true;
});

// Test 12: Message Processing
$testRunner->addTest('Message Processing', function() {
    // Test command parsing
    $parsed = Helper::parseCommand('/start hello world');
    TestRunner::assertEqual('/start', $parsed['command'], 'Command parsing failed');
    TestRunner::assertEqual('hello world', $parsed['arguments'], 'Arguments parsing failed');
    
    // Test command detection
    TestRunner::assertTrue(Helper::isCommand('/start'), 'Command not detected');
    TestRunner::assertTrue(!Helper::isCommand('hello'), 'Non-command detected as command');
    
    return true;
});

// Run all tests
$testRunner->run();
